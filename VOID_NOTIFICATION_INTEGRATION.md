# Void Notification Integration

## Overview
This document describes the integration of the void notification API call when a user clicks the void button on a cart item in the DasaPOS application.

## Implementation Details

### Modified File
- **`app/src/main/java/com/thedasagroup/suminative/ui/products/ProductsScreenViewModel.kt`**

### Changes Made

#### 1. Added Tracking Variables
In the `voidCartItem()` function, added two variables to track when an item is being voided:
```kotlin
// Track if we're voiding (not un-voiding) to send notification
var isVoidingItem = false
var voidedCart: Cart? = null
```

#### 2. Set Tracking Variables When Voiding
When an item is voided (not un-voided), the tracking variables are set:
```kotlin
} else {
    // Apply void - store original values and set prices to 0
    isVoidingItem = true
    
    // ... existing void logic ...
    
    voidedCart = voidedCartItem
    voidedCartItem
}
```

#### 3. Send Void Notification After State Update
After the `setState` block completes, the void notification is sent if an item was voided:

```kotlin
// Send void notification if an item was voided (not un-voided)
if (isVoidingItem && voidedCart != null) {
    viewModelScope.launch {
        try {
            val storeId = prefs.store?.id ?: return@launch
            
            // Get table name
            val tableName = if (currentTableId != null) {
                if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                    selectedTables[selectedTableIndex].tableName
                } else {
                    "Table-$currentTableId"
                }
            } else {
                "Takeaway"
            }
            
            // Get course name for this cart item
            val cartItemsWithCourses = if (currentTableId != null) {
                withState(this@ProductsScreenViewModel) { state ->
                    state.cartItemsWithCourses[currentTableId] ?: emptyList()
                }
            } else {
                withState(this@ProductsScreenViewModel) { state ->
                    state.globalCartItemsWithCourses
                }
            }
            
            val cartItemWithCourse = cartItemsWithCourses.find { 
                it.cart.uuid == voidedCart.uuid 
            }
            val courseName = cartItemWithCourse?.courseId ?: "Starters"
            
            // Send void notification with only the voided item
            sendVoidNotification(
                courseName = courseName,
                tableName = tableName,
                carts = listOf(voidedCart)
            )
        } catch (e: Exception) {
            Timber.e(e, "Error sending void notification")
        }
    }
}
```

## Behavior

### When Void Button is Clicked

1. **User clicks void button** on a cart item in the UI
2. **UI calls** `onVoidItem(cartItem)` callback
3. **Activity calls** `productsScreenViewModel.voidCartItem(...)` with the cart item
4. **ViewModel processes** the void operation:
   - If item is already voided → Un-voids it (restores original values) → **No notification sent**
   - If item is not voided → Voids it (sets prices to 0) → **Notification sent**
5. **State is updated** with the voided cart item
6. **Void notification is sent** to the printer API with:
   - **Store ID**: Current store ID from preferences
   - **Course Name**: The course assigned to the cart item (e.g., "Starters", "Mains", "Desserts")
   - **Table Name**: 
     - If table is selected: The table name (e.g., "Table 1")
     - If no table: "Takeaway"
   - **Cart JSON**: JSON representation of **only the voided item** (not all items)

### Key Features

✅ **Only sends notification when voiding** (not when un-voiding)  
✅ **Sends only the specific voided item** (not all cart items)  
✅ **Automatically determines course name** from cart item assignment  
✅ **Automatically determines table name** from selected table or defaults to "Takeaway"  
✅ **Error handling** with Timber logging  
✅ **Asynchronous** using coroutines (doesn't block UI)  

## Flow Diagram

```
User clicks Void Button
         ↓
CartScreenFigma.kt: onClick = { onVoidItem(cartItem) }
         ↓
CategoriesActivity.kt: onVoidItem = { cart -> productsScreenViewModel.voidCartItem(...) }
         ↓
ProductsScreenViewModel.kt: voidCartItem()
         ↓
    ┌────────────────────────────────┐
    │ Is item already voided?        │
    └────────────────────────────────┘
         ↓                    ↓
        YES                  NO
         ↓                    ↓
    Un-void item         Void item
    (Restore prices)     (Set prices to 0)
         ↓                    ↓
    No notification      Set isVoidingItem = true
                         Set voidedCart = item
         ↓                    ↓
    Update State ←───────────┘
         ↓
    Check if isVoidingItem == true
         ↓
        YES
         ↓
    Launch coroutine
         ↓
    Get storeId, tableName, courseName
         ↓
    Call sendVoidNotification(courseName, tableName, [voidedCart])
         ↓
    API call to /api/printer/voidNotification
```

## Testing

To test the implementation:

1. **Add items to cart** with different courses
2. **Click void button** on an item
3. **Verify**:
   - Item is voided in the UI (prices set to 0, "VOIDED" added to notes)
   - API call is made to `/api/printer/voidNotification`
   - Request contains correct storeId, courseName, tableName, and cartJson
   - Only the voided item is in the cartJson (not all items)
4. **Click void button again** on the same item (un-void)
5. **Verify**:
   - Item is un-voided in the UI (prices restored)
   - **No API call is made** (notification only sent when voiding, not un-voiding)

## Error Handling

- If `storeId` is null, the notification is not sent (early return)
- If any exception occurs during the API call, it's logged with Timber
- The void operation in the UI still completes successfully even if the notification fails

## Related Files

- **Request Model**: `app/src/main/java/com/thedasagroup/suminative/data/model/request/print/VoidNotificationRequest.kt`
- **Response Model**: `app/src/main/java/com/thedasagroup/suminative/data/model/response/void_notification/VoidNotificationResponse.kt`
- **Use Case**: `app/src/main/java/com/thedasagroup/suminative/domain/void_notification/SendVoidNotificationUseCase.kt`
- **Repository**: `app/src/main/java/com/thedasagroup/suminative/data/repo/ReservationsRepository.kt`
- **API Client**: `app/src/main/java/com/thedasagroup/suminative/data/api/ApiClient.kt`

## API Endpoint

```
POST https://dasasplace.com/BackendDASA-1.0.0/api/printer/voidNotification

Headers:
  Content-Type: application/json

Body:
{
  "storeId": 158,
  "courseName": "Starters",
  "tableName": "Table 1",
  "cartJson": "[{...}]"  // JSON array with only the voided item
}

Response:
{
  "success": true,
  "message": null,
  "data": null,
  "statusCode": 200
}
```

