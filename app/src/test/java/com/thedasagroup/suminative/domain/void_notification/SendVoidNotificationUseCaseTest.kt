package com.thedasagroup.suminative.domain.void_notification

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.request.print.VoidNotificationRequest
import com.thedasagroup.suminative.data.model.response.void_notification.VoidNotificationResponse
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for SendVoidNotificationUseCase
 */
class SendVoidNotificationUseCaseTest {

    private lateinit var reservationsRepository: ReservationsRepository
    private lateinit var useCase: SendVoidNotificationUseCase

    @Before
    fun setup() {
        reservationsRepository = mockk()
        useCase = SendVoidNotificationUseCase(reservationsRepository)
    }

    @Test
    fun `test sendVoidNotification with request object returns success`() = runBlocking {
        // Arrange
        val request = VoidNotificationRequest(
            storeId = 158,
            courseName = "Starters",
            tableName = "Takeaway",
            cartJson = """[{"storeItem":{"id":4280,"name":"BEERS"},"quantity":1}]"""
        )
        
        val expectedResponse = VoidNotificationResponse(
            success = true,
            message = null,
            data = null,
            statusCode = 200
        )
        
        val mockFlow = MutableStateFlow<Async<VoidNotificationResponse>>(Success(expectedResponse))
        
        coEvery { 
            reservationsRepository.sendVoidNotification(request) 
        } returns mockFlow

        // Act
        val result = useCase(request)

        // Assert
        assert(result.value is Success)
        val successResult = (result.value as Success).invoke()
        assert(successResult.success)
        assert(successResult.statusCode == 200)
    }

    @Test
    fun `test sendVoidNotification with individual parameters`() = runBlocking {
        // Arrange
        val storeId = 158
        val courseName = "Starters"
        val tableName = "Takeaway"
        val cartJson = """[{"storeItem":{"id":4280,"name":"BEERS"},"quantity":1}]"""
        
        val expectedResponse = VoidNotificationResponse(
            success = true,
            message = null,
            data = null,
            statusCode = 200
        )
        
        val mockFlow = MutableStateFlow<Async<VoidNotificationResponse>>(Success(expectedResponse))
        
        coEvery { 
            reservationsRepository.sendVoidNotification(storeId, courseName, tableName, cartJson) 
        } returns mockFlow

        // Act
        val result = useCase(storeId, courseName, tableName, cartJson)

        // Assert
        assert(result.value is Success)
        val successResult = (result.value as Success).invoke()
        assert(successResult.success)
    }

    @Test
    fun `test sendVoidNotification with Cart list`() = runBlocking {
        // Arrange
        val storeId = 158
        val courseName = "Starters"
        val tableName = "Takeaway"
        
        val storeItem = StoreItem(
            id = 4280,
            name = "BEERS",
            price = 11.7
        )
        
        val carts = listOf(
            Cart(
                storeItem = storeItem,
                quantity = 1,
                price = 0.0,
                extraPrice = 0.0,
                optionPrice = 0.0,
                tax = 0.0,
                discount = 0.0,
                netPayable = 11.7,
                isB1G1 = false,
                orderNotes = ""
            )
        )
        
        val expectedResponse = VoidNotificationResponse(
            success = true,
            message = null,
            data = null,
            statusCode = 200
        )
        
        val mockFlow = MutableStateFlow<Async<VoidNotificationResponse>>(Success(expectedResponse))
        
        coEvery { 
            reservationsRepository.sendVoidNotification(
                storeId = storeId,
                courseName = courseName,
                tableName = tableName,
                cartJson = any()
            ) 
        } returns mockFlow

        // Act
        val result = useCase(storeId, courseName, tableName, carts)

        // Assert
        assert(result.value is Success)
        val successResult = (result.value as Success).invoke()
        assert(successResult.success)
        assert(successResult.statusCode == 200)
    }

    @Test
    fun `test sendVoidNotification handles failure response`() = runBlocking {
        // Arrange
        val storeId = 158
        val courseName = "Starters"
        val tableName = "Takeaway"
        val cartJson = """[{"storeItem":{"id":4280,"name":"BEERS"},"quantity":1}]"""
        
        val expectedResponse = VoidNotificationResponse(
            success = false,
            message = "Printer not available",
            data = null,
            statusCode = 500
        )
        
        val mockFlow = MutableStateFlow<Async<VoidNotificationResponse>>(Success(expectedResponse))
        
        coEvery { 
            reservationsRepository.sendVoidNotification(storeId, courseName, tableName, cartJson) 
        } returns mockFlow

        // Act
        val result = useCase(storeId, courseName, tableName, cartJson)

        // Assert
        assert(result.value is Success)
        val successResult = (result.value as Success).invoke()
        assert(!successResult.success)
        assert(successResult.message == "Printer not available")
        assert(successResult.statusCode == 500)
    }

    @Test
    fun `test sendVoidNotification with empty cart list`() = runBlocking {
        // Arrange
        val storeId = 158
        val courseName = "Starters"
        val tableName = "Takeaway"
        val carts = emptyList<Cart>()
        
        val expectedResponse = VoidNotificationResponse(
            success = true,
            message = null,
            data = null,
            statusCode = 200
        )
        
        val mockFlow = MutableStateFlow<Async<VoidNotificationResponse>>(Success(expectedResponse))
        
        coEvery { 
            reservationsRepository.sendVoidNotification(
                storeId = storeId,
                courseName = courseName,
                tableName = tableName,
                cartJson = any()
            ) 
        } returns mockFlow

        // Act
        val result = useCase(storeId, courseName, tableName, carts)

        // Assert
        assert(result.value is Success)
        val successResult = (result.value as Success).invoke()
        assert(successResult.success)
    }
}

