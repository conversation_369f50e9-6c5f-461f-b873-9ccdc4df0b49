package com.thedasagroup.suminative.ui.pos_orders

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.request.pagination.POSOrderResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.orders.GetAllPOSOrdersUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

data class POSOrdersState(
    val ordersResponse: Async<POSOrderResponse> = Uninitialized,
    val currentPage: Int = 0,
    val pageSize: Int = 10,
    val totalCount: Int = 0,
    val isLoadingMore: Boolean = false,
    val hasMorePages: Boolean = true,
    val isShowPrintingPreview: OrderItem2? = null,
    val shouldPrintInstant: Boolean = false
) : MavericksState

class POSOrdersViewModel @AssistedInject constructor(
    @Assisted initialState: POSOrdersState,
    private val getAllPOSOrdersUseCase: GetAllPOSOrdersUseCase,
    val prefs: Prefs
) : MavericksViewModel<POSOrdersState>(initialState) {

    init {
        viewModelScope.launch(Dispatchers.IO) {
            fetchOrders(page = 0, state = initialState)
        }
    }

    fun fetchOrders(page: Int = 0, append: Boolean = false, state: POSOrdersState) {
        val pageSize = state.pageSize

        if (!append) {
            setState { copy(ordersResponse = Loading(), currentPage = page) }
        } else {
            setState { copy(isLoadingMore = true) }
        }

        viewModelScope.launch(Dispatchers.IO) {
            getAllPOSOrdersUseCase(
                pageNumber = page,
                pageSize = pageSize
            ).execute { result ->
                when (result) {
                    is Success -> {
                        val response = result()
                        when(response){
                            is Fail<*> -> {
                                copy(
                                    ordersResponse = Fail(Throwable((response as Fail<*>).error.message)),
                                    currentPage = page,
                                    totalCount = totalCount,
                                    isLoadingMore = false,
                                )
                            }
                            is Success<*> ->{
                                val posOrderResponse = response()
                                val currentOrders = if (append) {
                                    val existingOrders = when (val asyncResponse = state.ordersResponse) {
                                        is Success -> asyncResponse().orders ?: emptyList()
                                        else -> emptyList()
                                    }
                                    existingOrders + (posOrderResponse?.orders ?: emptyList())
                                } else {
                                    posOrderResponse?.orders ?: emptyList()
                                }

                                val totalCount = posOrderResponse?.totalCount
                                val hasMore = currentOrders.size < (totalCount ?: 0)

                                copy(
                                    ordersResponse = Success(
                                        POSOrderResponse(
                                            orders = currentOrders,
                                            totalCount = totalCount ?: 0,
                                            success = true
                                        )
                                    ),
                                    currentPage = page,
                                    totalCount = totalCount ?: 0,
                                    isLoadingMore = false,
                                    hasMorePages = hasMore
                                )
                            }
                            is Loading -> {
                                copy(
                                    currentPage = page,
                                    totalCount = totalCount,
                                    isLoadingMore = true
                                )
                            }
                            else -> {
                                copy(
                                    ordersResponse = Uninitialized,
                                    currentPage = page,
                                    totalCount = totalCount,
                                    isLoadingMore = false
                                )
                            }
                        }

                    }
                    is Fail -> {
                        copy(
                            ordersResponse = Fail(Throwable(result.error.message)),
                            currentPage = page,
                            totalCount = totalCount,
                            isLoadingMore = false
                        )
                    }
                    else -> {
                        if (append) {
                            copy(isLoadingMore = false)
                        } else {
                            copy(ordersResponse = Uninitialized, isLoadingMore = false)
                        }
                    }
                }
            }
        }
    }

    fun loadNextPage(state: POSOrdersState) {
        if (!state.isLoadingMore && state.hasMorePages) {
            fetchOrders(page = state.currentPage + 1, append = true, state = state)
        }
    }

    fun refresh(state: POSOrdersState) {
        fetchOrders(page = 0, append = false, state = state)
    }

    fun updateShowPrintingPreview(order: OrderItem2?, shouldPrintInstant: Boolean = false) {
        setState {
            copy(isShowPrintingPreview = order, shouldPrintInstant = shouldPrintInstant)
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<POSOrdersViewModel, POSOrdersState> {
        override fun create(state: POSOrdersState): POSOrdersViewModel
    }

    companion object : MavericksViewModelFactory<POSOrdersViewModel, POSOrdersState> by hiltMavericksViewModelFactory()
}

