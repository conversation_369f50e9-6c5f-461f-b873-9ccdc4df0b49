package com.thedasagroup.suminative.ui.reservations

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest
import com.thedasagroup.suminative.data.model.response.reservations.Area
import com.thedasagroup.suminative.data.model.response.reservations.Table
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

@AndroidEntryPoint
class CreateReservationActivity : ComponentActivity() {

    private val viewModel: ReservationsViewModel by viewModel()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SumiNativeTheme {
                CreateReservationWizard(
                    viewModel = viewModel,
                    onBackPressed = { finish() },
                    onReservationCreated = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateReservationWizard(
    viewModel: ReservationsViewModel,
    onBackPressed: () -> Unit,
    onReservationCreated: () -> Unit
) {
    val state by viewModel.collectAsState()
    val context = LocalContext.current

    // Wizard state
    var currentStep by remember { mutableStateOf(1) }

    // Form state
    var selectedPartySize by remember { mutableStateOf<Int?>(null) }
    var selectedDate by remember { mutableStateOf("") }
    var selectedArea by remember { mutableStateOf<Area?>(null) }
    var selectedTable by remember { mutableStateOf<Table?>(null) }
    var selectedTime by remember { mutableStateOf("") }
    var customerName by remember { mutableStateOf("") }
    var customerPhone by remember { mutableStateOf("") }

    // Load areas when screen opens
    LaunchedEffect(Unit) {
        viewModel.loadReservationAreas()
    }

    // Load tables when area is selected
    LaunchedEffect(selectedArea) {
        selectedArea?.let { area ->
            viewModel.loadTablesForArea(area.id)
        }
    }

    // Handle successful reservation creation
    LaunchedEffect(state.createReservationResponse) {
        if (state.createReservationResponse is Success) {
            onReservationCreated()
        }
    }

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = Color(0xFFF5F5F5)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp)
        ) {
            // Left Column - Title and Selection Preview
            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(end = 24.dp),
                verticalArrangement = Arrangement.Top
            ) {
                // Header
                Text(
                    text = "Tables Reservation",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1A237E),
                    modifier = Modifier.padding(top = 40.dp, bottom = 8.dp)
                )

                Text(
                    text = "Complete your reservation in a few simple steps",
                    fontSize = 16.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(bottom = 32.dp)
                )

                // Step Indicator
                StepIndicator(
                    currentStep = currentStep,
                    totalSteps = 6,
                    modifier = Modifier.padding(bottom = 32.dp)
                )

                // Selection Preview Card
                SelectionPreviewCard(
                    currentStep = currentStep,
                    selectedPartySize = selectedPartySize,
                    selectedDate = selectedDate,
                    selectedArea = selectedArea,
                    selectedTable = selectedTable,
                    selectedTime = selectedTime,
                    customerName = customerName,
                    customerPhone = customerPhone
                )
            }

            // Right Column - Current Step Content
            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // Content based on current step
                when (currentStep) {
                    1 -> PartySelectionStep(
                        selectedPartySize = selectedPartySize,
                        onPartySizeSelected = { selectedPartySize = it },
                        onNext = { currentStep = 2 }
                    )

                    2 -> DateSelectionStep(
                        selectedDate = selectedDate,
                        selectedPartySize = selectedPartySize,
                        onDateSelected = { selectedDate = it },
                        onNext = { currentStep = 3 },
                        onBack = { currentStep = 1 }
                    )

                    3 -> AreaSelectionStep(
                        selectedArea = selectedArea,
                        selectedPartySize = selectedPartySize,
                        selectedDate = selectedDate,
                        areasResponse = state.areasResponse,
                        onAreaSelected = { selectedArea = it },
                        onNext = { currentStep = 4 },
                        onBack = { currentStep = 2 }
                    )

                    4 -> TableSelectionStep(
                        selectedTable = selectedTable,
                        selectedPartySize = selectedPartySize,
                        selectedDate = selectedDate,
                        selectedArea = selectedArea,
                        tablesResponse = selectedArea?.let { state.areaTablesMap[it.id] },
                        onTableSelected = { selectedTable = it },
                        onNext = { currentStep = 5 },
                        onBack = { currentStep = 3 }
                    )

                    5 -> TimeSelectionStep(
                        selectedTime = selectedTime,
                        selectedPartySize = selectedPartySize,
                        selectedDate = selectedDate,
                        selectedArea = selectedArea,
                        selectedTable = selectedTable,
                        onTimeSelected = { selectedTime = it },
                        onNext = { currentStep = 6 },
                        onBack = { currentStep = 4 }
                    )

                    6 -> ContactDetailsStep(
                        customerName = customerName,
                        customerPhone = customerPhone,
                        selectedPartySize = selectedPartySize,
                        selectedDate = selectedDate,
                        selectedArea = selectedArea,
                        selectedTable = selectedTable,
                        selectedTime = selectedTime,
                        onNameChanged = { customerName = it },
                        onPhoneChanged = { customerPhone = it },
                        onConfirm = {
                            val reservationTime = "${selectedDate}T${selectedTime}"
                            val request = CreateReservationRequest(
                                id = null,
                                storeId = 158,
                                tableId = selectedTable!!.id,
                                customerId = 1,
                                guestName = customerName,
                                guestPhone = customerPhone,
                                numPeople = selectedPartySize ?: 1,
                                reservationStatus = 0,
                                reservationTime = reservationTime,
                                timezoneOffset = 0
                            )
                            viewModel.createReservation(request)
                        },
                        onBack = { currentStep = 5 },
                        isLoading = state.createReservationResponse is Loading
                    )
                }
            }
        }
    }
}

// Step Indicator Component
@Composable
fun StepIndicator(
    currentStep: Int,
    totalSteps: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFE3F2FD)),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            for (step in 1..totalSteps) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(
                                when {
                                    step < currentStep -> Color(0xFF4CAF50) // Green for completed
                                    step == currentStep -> Color(0xFF2196F3) // Blue for current
                                    else -> Color(0xFFE0E0E0) // Gray for upcoming
                                }
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = step.toString(),
                            color = if (step <= currentStep) Color.White else Color.Gray,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = when (step) {
                            1 -> "Party Size"
                            2 -> "Date"
                            3 -> "Section"
                            4 -> "Table"
                            5 -> "Time"
                            6 -> "Details"
                            else -> ""
                        },
                        fontSize = 12.sp,
                        color = if (step <= currentStep) Color(0xFF1A237E) else Color.Gray,
                        fontWeight = if (step == currentStep) FontWeight.Bold else FontWeight.Normal
                    )
                }
            }
        }
    }
}

// Selection Preview Card Component
@Composable
fun SelectionPreviewCard(
    currentStep: Int,
    selectedPartySize: Int?,
    selectedDate: String,
    selectedArea: Area?,
    selectedTable: Table?,
    selectedTime: String,
    customerName: String,
    customerPhone: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 20.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = Color(0xFF4CAF50),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "Your Selection",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1A237E)
                )
            }

            // Party Size
            if (selectedPartySize != null) {
                SelectionPreviewItem(
                    icon = Icons.Default.Group,
                    label = "Party Size",
                    value = "$selectedPartySize ${if (selectedPartySize == 1) "person" else "people"}",
                    isCompleted = true
                )
            } else {
                SelectionPreviewItem(
                    icon = Icons.Default.Group,
                    label = "Party Size",
                    value = "Not selected",
                    isCompleted = false,
                    isCurrent = currentStep == 1
                )
            }

            // Date
            if (selectedDate.isNotBlank()) {
                SelectionPreviewItem(
                    icon = Icons.Default.DateRange,
                    label = "Date",
                    value = selectedDate,
                    isCompleted = true
                )
            } else {
                SelectionPreviewItem(
                    icon = Icons.Default.DateRange,
                    label = "Date",
                    value = "Not selected",
                    isCompleted = false,
                    isCurrent = currentStep == 2
                )
            }

            // Area
            if (selectedArea != null) {
                SelectionPreviewItem(
                    icon = Icons.Default.Business,
                    label = "Section",
                    value = selectedArea.description,
                    isCompleted = true
                )
            } else {
                SelectionPreviewItem(
                    icon = Icons.Default.Business,
                    label = "Section",
                    value = "Not selected",
                    isCompleted = false,
                    isCurrent = currentStep == 3
                )
            }

            // Table
            if (selectedTable != null) {
                SelectionPreviewItem(
                    icon = Icons.Default.TableRestaurant,
                    label = "Table",
                    value = selectedTable.tableName,
                    isCompleted = true
                )
            } else {
                SelectionPreviewItem(
                    icon = Icons.Default.TableRestaurant,
                    label = "Table",
                    value = "Not selected",
                    isCompleted = false,
                    isCurrent = currentStep == 4
                )
            }

            // Time
            if (selectedTime.isNotBlank()) {
                SelectionPreviewItem(
                    icon = Icons.Default.Schedule,
                    label = "Time",
                    value = selectedTime,
                    isCompleted = true
                )
            } else {
                SelectionPreviewItem(
                    icon = Icons.Default.Schedule,
                    label = "Time",
                    value = "Not selected",
                    isCompleted = false,
                    isCurrent = currentStep == 5
                )
            }

            // Contact Details
            if (customerName.isNotBlank() && customerPhone.isNotBlank()) {
                SelectionPreviewItem(
                    icon = Icons.Default.Person,
                    label = "Contact",
                    value = "$customerName\n$customerPhone",
                    isCompleted = true
                )
            } else {
                SelectionPreviewItem(
                    icon = Icons.Default.Person,
                    label = "Contact",
                    value = "Not provided",
                    isCompleted = false,
                    isCurrent = currentStep == 6
                )
            }
        }
    }
}

@Composable
fun SelectionPreviewItem(
    icon: ImageVector,
    label: String,
    value: String,
    isCompleted: Boolean,
    isCurrent: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape)
                .background(
                    when {
                        isCompleted -> Color(0xFF4CAF50)
                        isCurrent -> Color(0xFF2196F3)
                        else -> Color(0xFFE0E0E0)
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = if (isCompleted || isCurrent) Color.White else Color.Gray,
                modifier = Modifier.size(16.dp)
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = label,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1A237E)
            )
            Text(
                text = value,
                fontSize = 16.sp,
                fontWeight = if (isCompleted) FontWeight.Bold else FontWeight.Normal,
                color = when {
                    isCompleted -> Color(0xFF4CAF50)
                    isCurrent -> Color(0xFF2196F3)
                    else -> Color.Gray
                }
            )
        }
    }
}

// Step 1: Party Size Selection
@Composable
fun PartySelectionStep(
    selectedPartySize: Int?,
    onPartySizeSelected: (Int) -> Unit,
    onNext: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Icon
        Icon(
            imageVector = Icons.Default.Group,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = Color(0xFF1A237E)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "How many guests?",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            textAlign = TextAlign.Center
        )

        Text(
            text = "Select the number of people in your party",
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 8.dp, bottom = 32.dp)
        )

        // Party size grid
        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.height(300.dp)
        ) {
            items((1..11).toList()) { size ->
                PartySizeCard(
                    size = size,
                    isSelected = selectedPartySize == size,
                    onClick = {
                        onPartySizeSelected(size)
                        // Auto-advance after selection
                        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main)
                            .launch {
                                kotlinx.coroutines.delay(300)
                                onNext()
                            }
                    }
                )
            }
        }
    }
}

@Composable
fun PartySizeCard(
    size: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clickable { onClick() }
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) Color(0xFF2196F3) else Color(0xFFE0E0E0),
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFFE3F2FD) else Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = if (size == 1) Icons.Default.Person else Icons.Default.Group,
                contentDescription = null,
                modifier = Modifier.size(24.dp),
                tint = if (isSelected) Color(0xFF2196F3) else Color(0xFF1A237E)
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = size.toString(),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = if (isSelected) Color(0xFF2196F3) else Color(0xFF1A237E)
            )

            Text(
                text = if (size == 1) "person" else "people",
                fontSize = 12.sp,
                color = Color.Gray
            )
        }
    }
}

// Step 2: Date Selection
@Composable
fun DateSelectionStep(
    selectedDate: String,
    selectedPartySize: Int?,
    onDateSelected: (String) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit
) {
    val context = LocalContext.current

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        // Calendar Icon
        Icon(
            imageVector = Icons.Default.DateRange,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = Color(0xFF1A237E)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "Pick a Date",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            textAlign = TextAlign.Center
        )

        Text(
            text = "When would you like to visit us?",
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 8.dp, bottom = 32.dp)
        )

        // Date Input
        OutlinedTextField(
            value = selectedDate,
            onValueChange = { },
            label = { Text("Select Date") },
            modifier = Modifier.fillMaxWidth(0.6f),
            readOnly = true,
            trailingIcon = {
                IconButton(
                    onClick = {
                        val calendar = Calendar.getInstance()
                        DatePickerDialog(
                            context,
                            { _, year, month, dayOfMonth ->
                                val date = Calendar.getInstance()
                                date.set(year, month, dayOfMonth)
                                val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                                onDateSelected(formatter.format(date.time))
                            },
                            calendar.get(Calendar.YEAR),
                            calendar.get(Calendar.MONTH),
                            calendar.get(Calendar.DAY_OF_MONTH)
                        ).show()
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.DateRange,
                        contentDescription = "Select Date"
                    )
                }
            }
        )

        Spacer(modifier = Modifier.height(48.dp))

        // Navigation Buttons
        NavigationButtons(
            onBack = onBack,
            onNext = onNext,
            nextEnabled = selectedDate.isNotBlank()
        )
    }
}

// Your Selection Card Component
@Composable
fun YourSelectionCard(
    partySize: Int? = null,
    date: String? = null,
    area: Area? = null,
    table: Table? = null,
    time: String? = null
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFE3F2FD)),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = Color(0xFF1A237E),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Your Selection",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1A237E)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                if (partySize != null) {
                    SelectionItem(
                        icon = Icons.Default.Group,
                        label = "Party Size:",
                        value = "$partySize ${if (partySize == 1) "person" else "people"}"
                    )
                }

                if (date != null) {
                    SelectionItem(
                        icon = Icons.Default.DateRange,
                        label = "Date:",
                        value = date
                    )
                }
            }

            if (area != null || table != null || time != null) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    if (area != null) {
                        SelectionItem(
                            icon = Icons.Default.Business,
                            label = "Area:",
                            value = area.description
                        )
                    }

                    if (table != null) {
                        SelectionItem(
                            icon = Icons.Default.TableRestaurant,
                            label = "Table:",
                            value = table.tableName
                        )
                    }
                }
            }

            if (time != null) {
                Spacer(modifier = Modifier.height(8.dp))
                SelectionItem(
                    icon = Icons.Default.Schedule,
                    label = "Time:",
                    value = time
                )
            }
        }
    }
}

@Composable
fun SelectionItem(
    icon: ImageVector,
    label: String,
    value: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Color(0xFF1A237E),
            modifier = Modifier.size(16.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = "$label $value",
            fontSize = 14.sp,
            color = Color(0xFF1A237E)
        )
    }
}

// Navigation Buttons Component
@Composable
fun NavigationButtons(
    onBack: (() -> Unit)? = null,
    onNext: (() -> Unit)? = null,
    nextEnabled: Boolean = true,
    nextText: String = "Next",
    isLoading: Boolean = false
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = if (onBack != null) Arrangement.SpaceBetween else Arrangement.Center
    ) {
        if (onBack != null) {
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier.height(48.dp),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.Gray
                )
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Back")
            }
        }

        if (onNext != null) {
            Button(
                onClick = onNext,
                enabled = nextEnabled && !isLoading,
                modifier = Modifier.height(48.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (nextText == "Confirm Reservation") Color(0xFF4CAF50) else Color.Black,
                    contentColor = Color.White
                )
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(18.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text(nextText)
                    if (nextText != "Confirm Reservation") {
                        Spacer(modifier = Modifier.width(8.dp))
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        }
    }
}

// Step 3: Area Selection
@Composable
fun AreaSelectionStep(
    selectedArea: Area?,
    selectedPartySize: Int?,
    selectedDate: String,
    areasResponse: com.airbnb.mvrx.Async<List<Area>>,
    onAreaSelected: (Area) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        // Building Icon
        Icon(
            imageVector = Icons.Default.Business,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = Color(0xFF1A237E)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "Choose Your Area",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            textAlign = TextAlign.Center
        )

        Text(
            text = "Select your preferred dining section",
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 8.dp, bottom = 32.dp)
        )

        // Areas Grid
        when (areasResponse) {
            is Success -> {
                val areas = areasResponse.invoke()
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier.height(300.dp)
                ) {
                    items(areas) { area ->
                        AreaCard(
                            area = area,
                            isSelected = selectedArea?.id == area.id,
                            onClick = {
                                onAreaSelected(area)
                                // Auto-advance after selection
                                CoroutineScope(Dispatchers.Main).launch {
                                    delay(300)
                                    onNext()
                                }
                            }
                        )
                    }
                }
            }

            else -> {
                Box(
                    modifier = Modifier.height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color(0xFF1A237E))
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        NavigationButtons(
            onBack = onBack,
            onNext = if (selectedArea != null) onNext else null,
            nextEnabled = selectedArea != null
        )
    }
}

@Composable
fun AreaCard(
    area: Area,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(120.dp)
            .clickable { onClick() }
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) Color(0xFF2196F3) else Color(0xFFE0E0E0),
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFFE3F2FD) else Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Restaurant,
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = if (isSelected) Color(0xFF2196F3) else Color(0xFF1A237E)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = area.description,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = if (isSelected) Color(0xFF2196F3) else Color(0xFF1A237E),
                textAlign = TextAlign.Center
            )
        }
    }
}

// Step 4: Table Selection
@Composable
fun TableSelectionStep(
    selectedTable: Table?,
    selectedPartySize: Int?,
    selectedDate: String,
    selectedArea: Area?,
    tablesResponse: com.airbnb.mvrx.Async<List<Table>>?,
    onTableSelected: (Table) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        // Table Icon
        Icon(
            imageVector = Icons.Default.TableRestaurant,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = Color(0xFF1A237E)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "Select Your Table",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            textAlign = TextAlign.Center
        )

        Text(
            text = "Choose the perfect spot for your party",
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 8.dp, bottom = 32.dp)
        )

        // Tables Grid
        when (tablesResponse) {
            is Success -> {
                val tables = tablesResponse.invoke()
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier.height(300.dp)
                ) {
                    items(tables) { table ->
                        TableCard(
                            table = table,
                            isSelected = selectedTable?.id == table.id,
                            onClick = {
                                onTableSelected(table)
                                // Auto-advance after selection
                                CoroutineScope(Dispatchers.Main).launch {
                                    delay(300)
                                    onNext()
                                }
                            }
                        )
                    }
                }
            }

            else -> {
                Box(
                    modifier = Modifier.height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color(0xFF1A237E))
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        NavigationButtons(
            onBack = onBack,
            onNext = if (selectedTable != null) onNext else null,
            nextEnabled = selectedTable != null
        )
    }
}

@Composable
fun TableCard(
    table: Table,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp)
            .clickable { onClick() }
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) Color(0xFF2196F3) else Color(0xFFE0E0E0),
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFFE3F2FD) else Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.TableRestaurant,
                contentDescription = null,
                modifier = Modifier.size(24.dp),
                tint = if (isSelected) Color(0xFF2196F3) else Color(0xFF1A237E)
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = table.tableName,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = if (isSelected) Color(0xFF2196F3) else Color(0xFF1A237E),
                textAlign = TextAlign.Center
            )

            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Group,
                    contentDescription = null,
                    modifier = Modifier.size(12.dp),
                    tint = Color.Gray
                )
                Spacer(modifier = Modifier.width(2.dp))
                Text(
                    text = "Seats ${table.seatingCapacity}",
                    fontSize = 10.sp,
                    color = Color.Gray
                )
            }
        }
    }
}

// Step 5: Time Selection
@Composable
fun TimeSelectionStep(
    selectedTime: String,
    selectedPartySize: Int?,
    selectedDate: String,
    selectedArea: Area?,
    selectedTable: Table?,
    onTimeSelected: (String) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        // Clock Icon
        Icon(
            imageVector = Icons.Default.Schedule,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = Color(0xFF1A237E)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "What Time?",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            textAlign = TextAlign.Center
        )

        Text(
            text = "Choose your preferred time slot",
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 8.dp, bottom = 32.dp)
        )

        // Time Slots
        val timeSlots = listOf("21:00", "21:30", "22:00", "22:30", "23:00", "23:30")

        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.height(200.dp)
        ) {
            items(timeSlots) { time ->
                TimeSlotCard(
                    time = time,
                    isSelected = selectedTime == time,
                    onClick = {
                        onTimeSelected(time)
                        // Auto-advance after selection
                        CoroutineScope(Dispatchers.Main).launch {
                            delay(300)
                            onNext()
                        }
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        NavigationButtons(
            onBack = onBack,
            onNext = if (selectedTime.isNotBlank()) onNext else null,
            nextEnabled = selectedTime.isNotBlank()
        )
    }
}

@Composable
fun TimeSlotCard(
    time: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp)
            .clickable { onClick() }
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) Color(0xFF2196F3) else Color(0xFFE0E0E0),
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFFE3F2FD) else Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Schedule,
                contentDescription = null,
                modifier = Modifier.size(20.dp),
                tint = if (isSelected) Color(0xFF2196F3) else Color(0xFF1A237E)
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = time,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = if (isSelected) Color(0xFF2196F3) else Color(0xFF1A237E),
                textAlign = TextAlign.Center
            )
        }
    }
}

// Step 6: Contact Details
@Composable
fun ContactDetailsStep(
    customerName: String,
    customerPhone: String,
    selectedPartySize: Int?,
    selectedDate: String,
    selectedArea: Area?,
    selectedTable: Table?,
    selectedTime: String,
    onNameChanged: (String) -> Unit,
    onPhoneChanged: (String) -> Unit,
    onConfirm: () -> Unit,
    onBack: () -> Unit,
    isLoading: Boolean
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        // Person Icon
        Icon(
            imageVector = Icons.Default.Person,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = Color(0xFF1A237E)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "Contact Details",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            textAlign = TextAlign.Center
        )

        Text(
            text = "We'll need your information to confirm the reservation",
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 8.dp, bottom = 32.dp)
        )

        // Contact Form
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Full Name Field
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(bottom = 8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = null,
                        tint = Color(0xFF1A237E),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Full Name",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF1A237E)
                    )
                }

                OutlinedTextField(
                    value = customerName,
                    onValueChange = onNameChanged,
                    placeholder = { Text("Enter your full name") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFF2196F3),
                        focusedLabelColor = Color(0xFF2196F3)
                    ),
                    shape = RoundedCornerShape(8.dp)
                )
            }

            // Phone Number Field
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(bottom = 8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Phone,
                        contentDescription = null,
                        tint = Color(0xFF1A237E),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Phone Number",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF1A237E)
                    )
                }

                OutlinedTextField(
                    value = customerPhone,
                    onValueChange = onPhoneChanged,
                    placeholder = { Text("Enter your phone number") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFF2196F3),
                        focusedLabelColor = Color(0xFF2196F3)
                    ),
                    shape = RoundedCornerShape(8.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Navigation Buttons
        val isFormValid = customerName.isNotBlank() && customerPhone.isNotBlank()

        NavigationButtons(
            onBack = onBack,
            onNext = if (isFormValid) onConfirm else null,
            nextEnabled = isFormValid,
            nextText = "Confirm Reservation",
            isLoading = isLoading
        )
    }
}