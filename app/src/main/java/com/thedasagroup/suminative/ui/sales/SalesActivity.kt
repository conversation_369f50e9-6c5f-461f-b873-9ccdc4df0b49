package com.thedasagroup.suminative.ui.sales

import android.app.ProgressDialog
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.printOrderBitmap
import com.thedasagroup.suminative.ui.common.CommonState
import com.thedasagroup.suminative.ui.common.CommonViewModel
import com.thedasagroup.suminative.ui.common.SuccessDialog
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SalesActivity : ComponentActivity(), MavericksView {
    val viewModel: ProductsScreenViewModel by viewModel()
    val commonViewModel : CommonViewModel by viewModel()
    lateinit var progressDialog : ProgressDialog
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        progressDialog = ProgressDialog(this)
        setContent {
            val coroutineScope = rememberCoroutineScope()
            val showSuccessDialog by commonViewModel.collectAsState(CommonState::showSuccessDialog)
            val successDialogMessage by commonViewModel.collectAsState(CommonState::successDialogMessage)
            SalesScreenMain(viewModel = viewModel, onBackClick = {
                finish()
            }, onPrintBill = { bitmap, salesRequest ->
                if(viewModel.prefs.storeConfigurations?.data?.cloudPrintersEnabledDefault == true || BuildConfig.DEBUG) {
                    coroutineScope.launch {
                        viewModel.printSalesReport(request = salesRequest)
                            .collectLatest { response ->
                                when (response) {
                                    is Success<*> -> {
                                        withContext(Dispatchers.Main) {
                                            commonViewModel.updateShowSuccessDialog(true)
                                            commonViewModel.updateSuccessDialogMessage("Sales Report Printed Successfully")
                                            progressDialog.hide()
                                            viewModel.showSalesReportDialog(show = false)
                                        }
                                    }

                                    is Fail -> {
                                        withContext(Dispatchers.Main) {
                                            commonViewModel.updateShowSuccessDialog(true)
                                            commonViewModel.updateSuccessDialogMessage("Sales Report Printing Failed")
                                            progressDialog.hide()
                                        }
                                    }

                                    is Loading<*> -> {
                                        withContext(Dispatchers.Main) {
                                            progressDialog.show()
                                        }
                                    }

                                    else -> {

                                    }
                                }
                            }
                    }
                }else {
                    printOrderBitmap(bitmap = bitmap, context = this)
                }
            })

            // Success Dialog
            SuccessDialog(
                isVisible = showSuccessDialog,
                message = successDialogMessage,
                onDismiss = {
                    commonViewModel.updateShowSuccessDialog(false)
                }
            )
        }
    }

    override fun onResume() {
        super.onResume()
        lifecycleScope.launch {
            viewModel.getStockItems()
        }
    }

    override fun invalidate() {

    }
}