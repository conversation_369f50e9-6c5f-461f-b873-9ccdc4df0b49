package com.thedasagroup.suminative.ui.orders

import android.graphics.Bitmap
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.airbnb.mvrx.compose.collectAsState
@Composable
fun ScheduleOrderScreenTopFunction(
    viewModel: OrderScreenViewModel,
    modifier: Modifier,
    callOrders: () -> Unit,
    onPrintBill: (Bitmap) -> Unit,
    onTrackingUrlClick: (String) -> Unit,
    onUpdateShowAllOrders: (Boolean) -> Unit
) {
    val ordersResponse by viewModel.collectAsState(OrderState::scheduleOrdersResponse)
    val isShowAllOrders by viewModel.collectAsState(OrderState::isShowAllOrders)

    ExpandableList2(
        viewModel = viewModel,
        modifier = modifier,
        callOrders = callOrders,
        onPrintBill = onPrintBill,
        onTrackingUrlClick = onTrackingUrlClick,
        onUpdateShowAllOrders = onUpdateShowAllOrders,
        ordersResponse = ordersResponse,
        isShowAllOrders = isShowAllOrders,
        shouldShowAllOrders = false
    )
}