package com.thedasagroup.suminative.ui.pos_orders

import android.graphics.Bitmap
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState

import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.ui.orders.PrintingPreviewDialog2
import com.thedasagroup.suminative.ui.orders.screenshotableComposable
import com.thedasagroup.suminative.ui.print.PrintingBill2
import com.thedasagroup.suminative.ui.print.verticalScrollbar
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.theme.fontNunito
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun POSOrdersScreen(
    viewModel: POSOrdersViewModel,
    productsScreenViewModel: ProductsScreenViewModel,
    onBackPressed: () -> Unit = {},
    onPrintBill: (Bitmap, OrderItem2) -> Unit
) {
    val state by viewModel.collectAsState()
    val ordersResponse by viewModel.collectAsState(POSOrdersState::ordersResponse)
    val isLoadingMore by viewModel.collectAsState(POSOrdersState::isLoadingMore)
    val hasMorePages by viewModel.collectAsState(POSOrdersState::hasMorePages)
    val totalCount by viewModel.collectAsState(POSOrdersState::totalCount)
    val isShowPrintingPreviewDialog by viewModel.collectAsState(POSOrdersState::isShowPrintingPreview)
    val coroutineScope = rememberCoroutineScope()

    // Print Preview Dialog
    if (isShowPrintingPreviewDialog != null) {
        PrintingPreviewDialog2(
            order = isShowPrintingPreviewDialog!!,
            onPrintBill = { bitmap ->
                onPrintBill(bitmap, isShowPrintingPreviewDialog!!)
            },
            onCancel = {
                viewModel.updateShowPrintingPreview(null, shouldPrintInstant = false)
            },
            productsScreenViewModel = productsScreenViewModel
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Column {
                        Text("POS Orders")
                        if (totalCount > 0) {
                            Text(
                                text = "Total: $totalCount orders",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                            )
                        }
                    }
                },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = {
                        coroutineScope.launch(Dispatchers.IO) {
                            viewModel.refresh(state = state)
                        }
                    }) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Refresh"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when (ordersResponse) {
                is Loading -> {
                    if (!isLoadingMore) {
                        CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
                    }
                }

                is Success -> {
                    val orders = ordersResponse()?.orders ?: emptyList()
                    if (orders.isEmpty()) {
                        Text(
                            text = "No orders found",
                            modifier = Modifier.align(Alignment.Center),
                            style = MaterialTheme.typography.bodyLarge
                        )
                    } else {
                        OrdersList(
                            orders = orders,
                            isLoadingMore = isLoadingMore,
                            hasMorePages = hasMorePages,
                            onLoadMore = {
                                coroutineScope.launch(Dispatchers.IO) {
                                    viewModel.loadNextPage(state = state)
                                }
                            },
                            onOrderClick = { orderItem ->
                                // Convert OrderItem to OrderItem2
                                val orderItem2 = OrderItem2(
                                    customer = orderItem.customer,
                                    order = orderItem.order,
                                    shouldClearCart = false
                                )
                                viewModel.updateShowPrintingPreview(orderItem2, shouldPrintInstant = false)
                            }
                        )
                    }
                }

                is Fail -> {
                    val error = (ordersResponse as Fail<*>).error.message ?: "Unknown error"
                    Column(
                        modifier = Modifier.align(Alignment.Center),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Error: $error",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = {
                            coroutineScope.launch(Dispatchers.IO) {
                                viewModel.refresh(state = state)
                            }
                        }) {
                            Text("Retry")
                        }
                    }
                }

                else -> {
                    // Initial state
                    Column {

                    }
                }
            }
        }
    }
}

@Composable
fun OrdersList(
    orders: List<OrderItem2>,
    isLoadingMore: Boolean,
    hasMorePages: Boolean,
    onLoadMore: () -> Unit,
    onOrderClick: (OrderItem2) -> Unit
) {
    val listState = rememberLazyListState()

    // Detect when user scrolls to the bottom
    val shouldLoadMore by remember {
        derivedStateOf {
            val lastVisibleItem = listState.layoutInfo.visibleItemsInfo.lastOrNull()
            lastVisibleItem != null && lastVisibleItem.index >= orders.size - 3 && hasMorePages && !isLoadingMore
        }
    }

    LaunchedEffect(shouldLoadMore) {
        if (shouldLoadMore) {
            onLoadMore()
        }
    }

    LazyColumn(
        state = listState,
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(orders) { orderItem ->
            OrderCard(
                orderItem = orderItem,
                onClick = { onOrderClick(orderItem) }
            )
        }

        // Loading indicator at the bottom
        if (isLoadingMore) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(modifier = Modifier.size(32.dp))
                }
            }
        }

        // End of list indicator
        if (!hasMorePages && orders.isNotEmpty()) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No more orders",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun OrderCard(orderItem: OrderItem2, onClick: () -> Unit = {}) {
    val order = orderItem.order
    val customer = orderItem.customer

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Order ID and Status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Order #${order?.id ?: "N/A"}",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    fontFamily = fontNunito
                )

                OrderStatusChip(status = order?.status ?: 0)
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Customer Info
            if (customer != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Customer: ",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = customer.name ?: "Guest",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    if (!customer.phone.isNullOrEmpty()) {
                        Text(
                            text = " • ${customer.phone}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
                Spacer(modifier = Modifier.height(4.dp))
            }

            // Order Date
            order?.createdOn?.let { dateString ->
                Text(
                    text = formatOrderDate(dateString),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
                Spacer(modifier = Modifier.height(8.dp))
            }

            HorizontalDivider()

            Spacer(modifier = Modifier.height(8.dp))

            // Order Details
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "Total Amount",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "£${(order?.netPayable ?: 0.0).transformDecimal()}",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }

                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = getPaymentTypeText(order?.paymentType ?: 1),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                }
            }
        }
    }
}

@Composable
fun OrderStatusChip(status: Int) {
    val (statusText, backgroundColor) = when (status) {
        0 -> "Pending" to Color(0xFFFFA726)
        1 -> "Accepted" to Color(0xFF66BB6A)
        2 -> "Preparing" to Color(0xFF42A5F5)
        3 -> "Ready" to Color(0xFF26C6DA)
        4 -> "Completed" to Color(0xFF4CAF50)
        5 -> "Cancelled" to Color(0xFFEF5350)
        else -> "Unknown" to Color.Gray
    }

    Surface(
        shape = RoundedCornerShape(12.dp),
        color = backgroundColor.copy(alpha = 0.2f),
        modifier = Modifier.padding(4.dp)
    ) {
        Text(
            text = statusText,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
            style = MaterialTheme.typography.labelSmall,
            color = backgroundColor,
            fontWeight = FontWeight.Bold
        )
    }
}

fun formatOrderDate(dateString: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
        inputFormat.timeZone = TimeZone.getTimeZone("UTC")
        val date = inputFormat.parse(dateString)
        val outputFormat = SimpleDateFormat("MMM dd, yyyy hh:mm a", Locale.getDefault())
        date?.let { outputFormat.format(it) } ?: dateString
    } catch (e: Exception) {
        dateString
    }
}

fun getDeliveryTypeText(type: Int): String {
    return when (type) {
        1 -> "Pickup"
        2 -> "Delivery"
        3 -> "Dine In"
        else -> "Unknown"
    }
}

fun getPaymentTypeText(type: Int): String {
    return when (type) {
        5 -> "Cash"
        6 -> "Card"
        else -> "Unknown"
    }
}