package com.thedasagroup.suminative.ui.products.cart

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCard
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.Splitscreen
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.LocalTextStyle
import androidx.compose.runtime.LaunchedEffect
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.stores.isMobilePOS
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import com.thedasagroup.suminative.ui.utils.transformDecimal1

@Composable
fun PayTabContent(
    state: ProductsScreenState,
    order: Order,
    placeOrderCard: () -> Unit,
    placeOrderCash: (Double) -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onSyncTable: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {

    var showSplitBillDialog by remember { mutableStateOf(false) }

    if (showSplitBillDialog) {
        SplitBillDialog(
            onDismiss = { showSplitBillDialog = false },
            onConfirm = { numberOfPersons ->
                showSplitBillDialog = false
                onSplitBillClick(numberOfPersons)
            }
        )
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Scrollable content
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(
                top = 16.dp,
                bottom = PayTabPaymentOptionsSize
            ) // Bottom padding for sticky payment section
        ) {
            // Order Summary Header
            item {
                Text(
                    text = "ORDER SUMMARY (${order.carts?.size ?: 0} items)",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // Order Items List
            order.carts?.filter { !it.isVoided }?.let { carts ->
                items(carts) { cartItem ->
                    PayTabOrderItem(
                        cartItem = cartItem,
                        onVoidItem = onVoidItem,
                        productsScreenViewModel = productsScreenViewModel
                    )
                }
            }
        }

        // Sticky Payment Section at the bottom
        PayTabPaymentSection(
            order = order,
            state = state,
            placeOrderCard = placeOrderCard,
            placeOrderCash = placeOrderCash,
            onSplitBillClick = { showSplitBillDialog = true },
            onSyncTable = onSyncTable,
            productsScreenViewModel = productsScreenViewModel,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}

@Composable
fun PayTabOrderItem(
    cartItem: Cart,
    onVoidItem: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Item name, void button, and quantity indicator
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.weight(0.6f)
        ) {

            Column {

                Text(
                    text = "${cartItem.quantity} x ${cartItem.storeItem?.name ?: "Unknown Item"}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                    color = if (cartItem.isVoided) Color.Gray else Color.Black,
                    fontFamily = fontPoppins,
                    textDecoration = if (cartItem.isVoided) androidx.compose.ui.text.style.TextDecoration.LineThrough else androidx.compose.ui.text.style.TextDecoration.None
                )

                cartItem.storeItem?.optionSets?.forEach { optionSet ->
                    optionSet.options?.forEach { option ->
                        if ((option?.quantity ?: 0) > 0) {
                            androidx.compose.material.Text(
                                text = "Options: x${option?.quantity} - ${option?.name ?: ""}",
                                fontSize = 12.sp,
                                color = if (cartItem.isVoided) Color.Gray else Color.Black.copy(alpha = 0.7f),
                                fontFamily = fontPoppins,
                                modifier = Modifier.padding(top = 2.dp),
                                textDecoration = if (cartItem.isVoided) androidx.compose.ui.text.style.TextDecoration.LineThrough else androidx.compose.ui.text.style.TextDecoration.None
                            )
                        }
                    }
                }
            }

            // Show options one by one below the product title

            //productsScreenViewModel.prefs.storeConfigurations?.data?.quickService != true
        }

        // Price - show 0 if voided, otherwise show actual price
        val displayPrice = if (cartItem.isVoided) 0.0 else (cartItem.netPayable ?: 0.0)
        Text(
            text = "£${displayPrice.transformDecimal()}",
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = if (cartItem.isVoided) Color.Gray else Color.Black,
            fontFamily = fontPoppins,
            modifier = Modifier.weight(0.2f),
            textAlign = TextAlign.End,
            textDecoration = if (cartItem.isVoided) androidx.compose.ui.text.style.TextDecoration.LineThrough else androidx.compose.ui.text.style.TextDecoration.None
        )
    }
}

@Composable
private fun PayTabPaymentSection(
    order: Order,
    state: ProductsScreenState,
    placeOrderCard: () -> Unit,
    placeOrderCash: (Double) -> Unit,
    onSplitBillClick: () -> Unit,
    onSyncTable: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel,
    modifier: Modifier = Modifier
) {
    val orderResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)

    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(PayTabPaymentOptionsSize),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Payment Options Header
            Text(
                text = "Payment Options",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )

            // Payment Buttons Row
            when (orderResponse) {

                is Loading -> {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF2E7D32)
                        )
                    }
                }

                else -> {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // Card Button
                        Button(
                            onClick = placeOrderCard,
                            modifier = Modifier
                                .weight(1f)
                                .height(48.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            if (!isMobilePOS) {
                                Icon(
                                    imageVector = Icons.Default.AddCard,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                            }
                            Text(
                                text = "Card",
                                fontWeight = FontWeight.Bold,
                                fontSize = 14.sp,
                                fontFamily = fontPoppins
                            )
                        }

                        // Cash Button
                        Button(
                            onClick = {
                                placeOrderCash(
                                    order.totalPrice(
                                        applyServiceCharge = state.isServiceChargeApplied(),
                                        serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
                                    )
                                )
                            },
                            modifier = Modifier
                                .weight(1f)
                                .height(48.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            if (!isMobilePOS) {
                                Icon(
                                    imageVector = Icons.Default.Money,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                            }
                            Text(
                                text = "Cash",
                                fontWeight = FontWeight.Bold,
                                fontSize = 14.sp,
                                fontFamily = fontPoppins
                            )
                        }

                        // Split Bill Button (outlined)
                        OutlinedButton(
                            onClick = onSplitBillClick,
                            modifier = Modifier
                                .weight(1f)
                                .height(48.dp),
                            shape = RoundedCornerShape(8.dp),
                            border = BorderStroke(1.dp, Color(0xFF2E7D32)),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color(0xFF2E7D32)
                            )
                        ) {
                            if (!isMobilePOS) {
                                Icon(
                                    imageVector = Icons.Default.Splitscreen,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                            }
                            Text(
                                text = "Split Bill",
                                fontWeight = FontWeight.Bold,
                                fontSize = 12.sp,
                                fontFamily = fontPoppins
                            )
                        }
                    }
                }
            }

            // Order Summary Section
            PayTabOrderSummary(order = order, productsScreenViewModel = productsScreenViewModel, state = state)
        }
    }
}

@Composable
fun PayTabOrderSummary(order: Order, productsScreenViewModel: ProductsScreenViewModel, state: ProductsScreenState) {
    val serviceCharge = state.getServiceChargeAmount(
        productsScreenViewModel.prefs.storeConfigurations?.data?.serviceChargePercentage ?: 0.0
    )

    val discountAmount = state.getDiscountAmount()

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.padding(bottom = if(isMobilePOS) 30.dp else 0.dp)
    ) {
        // Sub Total
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Sub Total",
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            Text(
                text = "£${order.net()?.transformDecimal() ?: "0.00"}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )
        }

        // Service Charge
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Service Charge (${productsScreenViewModel.prefs.storeConfigurations?.data?.serviceChargePercentage?.transformDecimal1() ?: 0.0}%)",
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            Text(
                text = "£${serviceCharge.transformDecimal()}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )
        }

        // Discount
        if (state.isDiscountApplied()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                androidx.compose.material.Text(
                    text = "Discount (${state.getAppliedDiscountPercentage().transformDecimal()}%)",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color.Red,
                    fontFamily = fontPoppins
                )
                androidx.compose.material.Text(
                    text = "-£${discountAmount.transformDecimal()}",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Red,
                    fontFamily = fontPoppins
                )
            }
        }

        // Total Payable with green background
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF2E7D32)
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Total Payable",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
                Text(
                    text = "£${
                        order.totalPrice(
                            applyServiceCharge = state.isServiceChargeApplied(),
                            serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
                        ).transformDecimal() ?: "0.00"}",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SplitBillDialog(
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit
) {
    var numberOfPersons by remember { mutableStateOf("") }
    var isError by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Split Bill",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )

                Text(
                    text = "Enter the number of persons to split the bill",
                    fontSize = 14.sp,
                    color = Color.Black.copy(alpha = 0.7f),
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.Center
                )

                OutlinedTextField(
                    value = numberOfPersons,
                    onValueChange = {
                        numberOfPersons = it
                        isError = false
                    },
                    label = {
                        Text(
                            "Number of Persons", fontFamily = fontPoppins,
                            textAlign = TextAlign.Center
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    isError = isError,
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        focusedLabelColor = Color(0xFF2E7D32)
                    ),
                    modifier = Modifier.fillMaxWidth(),
                    textStyle = LocalTextStyle.current.copy(textAlign = TextAlign.Center)
                )

                if (isError) {
                    Text(
                        text = "Please enter a valid number (1-20)",
                        fontSize = 12.sp,
                        color = Color.Red,
                        fontFamily = fontPoppins
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    TextButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Cancel",
                            color = Color.Gray,
                            fontFamily = fontPoppins
                        )
                    }

                    Button(
                        onClick = {
                            val persons = numberOfPersons.toIntOrNull()
                            if (persons != null && persons in 1..20) {
                                onConfirm(persons)
                            } else {
                                isError = true
                            }
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32)
                        )
                    ) {
                        Text(
                            text = "Confirm",
                            color = Color.White,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }
        }
    }
}

val PayTabPaymentOptionsSize = 300.dp