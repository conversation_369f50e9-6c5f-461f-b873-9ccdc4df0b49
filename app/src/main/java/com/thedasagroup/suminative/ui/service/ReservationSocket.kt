package com.thedasagroup.suminative.ui.service

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@Parcelize
data class ReservationTimeSocket(
    @SerialName("year") val year: Int? = null,
    @SerialName("month") val month: String? = null,
    @SerialName("monthValue") val monthValue: Int? = null,
    @SerialName("dayOfMonth") val dayOfMonth: Int? = null,
    @SerialName("hour") val hour: Int? = null,
    @SerialName("minute") val minute: Int? = null,
    @SerialName("second") val second: Int? = null,
    @SerialName("nano") val nano: Int? = null,
    @SerialName("dayOfWeek") val dayOfWeek: String? = null,
    @SerialName("dayOfYear") val dayOfYear: Int? = null,
    @SerialName("chronology") val chronology: ChronologySocket? = null
) : Parcelable

@Serializable
@Parcelize
data class ChronologySocket(
    @SerialName("calendarType") val calendarType: String? = null,
    @SerialName("id") val id: String? = null
) : Parcelable

@Serializable
@Parcelize
data class ReservationSocket(
    @SerialName("id") val id: Int? = null,
    @SerialName("storeId") val storeId: Int? = null,
    @SerialName("tableId") val tableId: Int? = null,
    @SerialName("serviceId") val serviceId: Int? = null,
    @SerialName("serviceProviderId") val serviceProviderId: Int? = null,
    @SerialName("serviceAddonId") val serviceAddonId: Int? = null,
    @SerialName("customerId") val customerId: Int? = null,
    @SerialName("reservationStatus") val reservationStatus: Int? = null,
    @SerialName("guestName") val guestName: String? = null,
    @SerialName("guestPhone") val guestPhone: String? = null,
    @SerialName("reservationTime") val reservationTime: ReservationTimeSocket? = null,
    @SerialName("numPeople") val numPeople: Int? = null,
    @SerialName("reservationType") val reservationType: String? = null,
    @SerialName("createdAt") val createdAt: ReservationTimeSocket? = null
) : Parcelable
