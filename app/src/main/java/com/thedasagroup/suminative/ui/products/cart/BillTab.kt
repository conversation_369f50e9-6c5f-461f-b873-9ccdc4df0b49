package com.thedasagroup.suminative.ui.products.cart

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.FormatListBulleted
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.RemoveCircle
import androidx.compose.material.icons.filled.Splitscreen
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.stores.isMobilePOS
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import com.thedasagroup.suminative.ui.utils.transformDecimal1

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun BillTabContent(
    order: Order,
    state: ProductsScreenState,
    onVoidItem: (Cart) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onApplyServiceChargeClick: () -> Unit,
    onRemoveServiceChargeClick: () -> Unit,
    onApplyDiscountClick: (Double) -> Unit,
    onRemoveDiscountClick: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {

    Box(modifier = Modifier.fillMaxSize()) {
        // Scrollable content
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(top = 16.dp, bottom = BillOptionsSize) // Bottom padding for bill summary
        ) {
            // Order Summary Header
            item {
                Text(
                    text = "BILL SUMMARY (${order.carts?.size ?: 0} items)",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // Order Items List
            order.carts?.let { carts ->
                items(carts) { cartItem ->
                    BillTabOrderItem(
                        cartItem = cartItem,
                        onVoidItem = onVoidItem,
                        productsScreenViewModel = productsScreenViewModel
                    )
                }
            }
        }

        // Sticky Bill Summary Section at the bottom
        BillTabPaymentSection(
            order = order,
            state = state,
            onPrintBill = {
                onCloudPrintClick(order)
            },
            productsScreenViewModel = productsScreenViewModel,
            onApplyServiceChargeClick = {
                onApplyServiceChargeClick()
            },
            onRemoveServiceChargeClick = onRemoveServiceChargeClick,
            onApplyDiscountClick = onApplyDiscountClick,
            onRemoveDiscountClick = onRemoveDiscountClick,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}

@Composable
fun BillTabOrderItem(
    cartItem: Cart,
    onVoidItem: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Item name, void button, and quantity indicator
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.weight(0.6f)
        ) {

            Column {

                androidx.compose.material3.Text(
                    text = "${cartItem.quantity} x ${cartItem.storeItem?.name ?: "Unknown Item"}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                    color = if (cartItem.isVoided) Color.Gray else Color.Black,
                    fontFamily = fontPoppins,
                    textDecoration = if (cartItem.isVoided) TextDecoration.LineThrough else TextDecoration.None
                )

                cartItem.storeItem?.optionSets?.forEach { optionSet ->
                    optionSet.options?.forEach { option ->
                        if ((option?.quantity ?: 0) > 0) {
                            androidx.compose.material.Text(
                                text = "Options: x${option?.quantity} - ${option?.name ?: ""}",
                                fontSize = 12.sp,
                                color = if (cartItem.isVoided) Color.Gray else Color.Black.copy(alpha = 0.7f),
                                fontFamily = fontPoppins,
                                modifier = Modifier.padding(top = 2.dp),
                                textDecoration = if (cartItem.isVoided) androidx.compose.ui.text.style.TextDecoration.LineThrough else androidx.compose.ui.text.style.TextDecoration.None
                            )
                        }
                    }
                }
            }

            // Show options one by one below the product title

            //productsScreenViewModel.prefs.storeConfigurations?.data?.quickService != true
        }

        // Void Button (only show if not quick service)
        if (true) {
            androidx.compose.material3.TextButton(
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (cartItem.isVoided) Color(0xFF4CAF50) else Color.Red,
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(8.dp),
                onClick = { onVoidItem(cartItem) },
                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                modifier = Modifier
                    .weight(0.2f)
                    .height(24.dp)
            ) {
                Text(
                    text = if (cartItem.isVoided) "Voided" else "Void",
                    fontSize = 12.sp,
                    color = Color.White,
                    fontFamily = fontPoppins,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        // Price - show 0 if voided, otherwise show actual price
        val displayPrice = if (cartItem.isVoided) 0.0 else (cartItem.netPayable ?: 0.0)
        Text(
            text = "£${displayPrice.transformDecimal()}",
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = if (cartItem.isVoided) Color.Gray else Color.Black,
            fontFamily = fontPoppins,
            modifier = Modifier.weight(0.2f),
            textAlign = TextAlign.End,
            textDecoration = if (cartItem.isVoided) androidx.compose.ui.text.style.TextDecoration.LineThrough else androidx.compose.ui.text.style.TextDecoration.None
        )
    }
}

@Composable
private fun BillTabPaymentSection(
    order: Order,
    state: ProductsScreenState,
    onPrintBill: () -> Unit,
    onApplyServiceChargeClick: () -> Unit,
    onRemoveServiceChargeClick: () -> Unit,
    onApplyDiscountClick: (Double) -> Unit,
    onRemoveDiscountClick: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel,
    modifier: Modifier = Modifier
) {

    val orderResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)

    androidx.compose.material3.Card(
        modifier = modifier
            .fillMaxWidth()
            .height(BillOptionsSize),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Payment Options Header
            Text(
                text = "Bill Options",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )

            // Payment Buttons Row
            when (orderResponse) {

                is Loading -> {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF2E7D32)
                        )
                    }
                }

                else -> {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            ApplyServiceChargeButton(
                                order = order, productsScreenViewModel = productsScreenViewModel,
                                onApplyServiceChargeClick = onApplyServiceChargeClick,
                                onRemoveServiceChargeClick = onRemoveServiceChargeClick,
                                modifier = Modifier
                                    .weight(1f)
                                    .height(64.dp)
                            )

                            ApplyDiscountButton(
                                order = order, productsScreenViewModel = productsScreenViewModel,
                                onApplyDiscountClick = onApplyDiscountClick,
                                onRemoveDiscountClick = onRemoveDiscountClick,
                                modifier = Modifier
                                    .weight(1f)
                                    .height(64.dp)
                            )
                        }

                        // Card Button
                        Button(
                            onClick = onPrintBill,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            if (!isMobilePOS) {
                                Icon(
                                    imageVector = Icons.Default.FormatListBulleted,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp),
                                    tint = Color.White
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                            }
                            Text(
                                text = "Print Bill",
                                fontWeight = FontWeight.Bold,
                                fontSize = 14.sp,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    }
                }
            }
            BillTabOrderSummary(
                order = order,
                state = state,
                productsScreenViewModel = productsScreenViewModel
            )
        }
    }
}

@Composable
private fun ApplyServiceChargeButton(
    order: Order,
    onApplyServiceChargeClick: () -> Unit,
    onRemoveServiceChargeClick: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel,
    modifier: Modifier
) {
    val state by productsScreenViewModel.collectAsState()
    val serviceChargeApplied = state.isServiceChargeApplied()
    val serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
    if (serviceChargeApplied) {
        // Remove Service Charge Button
        androidx.compose.material.OutlinedButton(
            onClick = { onRemoveServiceChargeClick() },
            modifier = modifier
                .height(64.dp),
            shape = RoundedCornerShape(8.dp),
            border = BorderStroke(1.dp, Color.Red),
            colors = androidx.compose.material.ButtonDefaults.outlinedButtonColors(
                contentColor = Color.Red
            )
        ) {
            Icon(
                imageVector = Icons.Default.RemoveCircle,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Service Charge (${serviceChargePercentage.transformDecimal()}%)",
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp,
                fontFamily = fontPoppins,
            )
        }
    } else {
        // Apply Service Charge Button
        OutlinedButton(
            onClick = { onApplyServiceChargeClick() },
            modifier = modifier
                .height(if(isMobilePOS) 80.dp else 64.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF2E7D32),
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.AddCircle,
                contentDescription = null,
                modifier = Modifier.size(18.dp),
                tint = Color.White
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Service Charge (${serviceChargePercentage.transformDecimal()}%)",
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp,
                fontFamily = fontPoppins,
                color = Color.White
            )
        }
    }
}

@Composable
private fun ApplyDiscountButton(
    order: Order,
    onApplyDiscountClick: (Double) -> Unit,
    onRemoveDiscountClick: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel,
    modifier: Modifier
) {
    val state by productsScreenViewModel.collectAsState()
    val discountApplied = state.isDiscountApplied()
    val discountPercentage = state.getAppliedDiscountPercentage()
    var showDiscountDialog by remember { mutableStateOf(false) }

    if (discountApplied) {
        // Remove Discount Button
        androidx.compose.material.OutlinedButton(
            onClick = { onRemoveDiscountClick() },
            modifier = modifier
                .height(64.dp),
            shape = RoundedCornerShape(8.dp),
            border = BorderStroke(1.dp, Color.Red),
            colors = androidx.compose.material.ButtonDefaults.outlinedButtonColors(
                contentColor = Color.Red
            )
        ) {
            Icon(
                imageVector = Icons.Default.RemoveCircle,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Discount (${discountPercentage.transformDecimal()}%)",
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp,
                fontFamily = fontPoppins,
            )
        }
    } else {
        // Apply Discount Button
        OutlinedButton(
            onClick = { showDiscountDialog = true },
            modifier = modifier
                .height(if(isMobilePOS) 80.dp else 64.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF2E7D32),
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.AddCircle,
                contentDescription = null,
                modifier = Modifier.size(18.dp),
                tint = Color.White
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Apply Discount",
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp,
                fontFamily = fontPoppins,
                color = Color.White
            )
        }
    }

    // Discount Dialog
    if (showDiscountDialog) {
        DiscountPercentageDialog(
            onDismiss = { showDiscountDialog = false },
            onConfirm = { percentage ->
                onApplyDiscountClick(percentage)
                showDiscountDialog = false
            },
            productsScreenViewModel = productsScreenViewModel
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DiscountPercentageDialog(
    onDismiss: () -> Unit,
    onConfirm: (Double) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val storeConfigurations = productsScreenViewModel.prefs.storeConfigurations?.data
    val discountInterval = storeConfigurations?.discountValueInterval ?: 1.0
    val maxDiscount = storeConfigurations?.maxStoreWidePOSDiscount ?: 50.0

    var selectedPercentage by remember { mutableStateOf(discountInterval) }

    // Generate discount options based on interval
    val discountOptions = mutableListOf<Double>()
    var current = discountInterval
    while (current <= maxDiscount) {
        discountOptions.add(current)
        current += discountInterval
    }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Select Discount Percentage",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )

                Text(
                    text = "Choose a discount percentage to apply to the order",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    fontFamily = fontPoppins
                )

                // Discount percentage selector
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    modifier = Modifier.height(200.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(discountOptions) { percentage ->
                        val isSelected = selectedPercentage == percentage
                        androidx.compose.material3.Card(
                            modifier = Modifier
                                .aspectRatio(1f)
                                .clickable { selectedPercentage = percentage },
                            colors = CardDefaults.cardColors(
                                containerColor = if (isSelected) Color(0xFF2E7D32) else Color.White
                            ),
                            border = BorderStroke(
                                width = 2.dp,
                                color = if (isSelected) Color(0xFF2E7D32) else Color.Gray.copy(alpha = 0.3f)
                            )
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "${percentage.toInt()}%",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = if (isSelected) Color.White else Color.Black,
                                    fontFamily = fontPoppins
                                )
                            }
                        }
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    androidx.compose.material3.OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        )
                    ) {
                        Text(
                            text = "Cancel",
                            fontFamily = fontPoppins
                        )
                    }

                    Button(
                        onClick = { onConfirm(selectedPercentage) },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32)
                        )
                    ) {
                        Text(
                            text = "Apply",
                            fontFamily = fontPoppins,
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun BillTabOrderSummary(
    order: Order,
    state: ProductsScreenState,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val serviceCharge = state.getServiceChargeAmount(
        productsScreenViewModel.prefs.storeConfigurations?.data?.serviceChargePercentage ?: 0.0
    )
    val discountAmount = state.getDiscountAmount()

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.padding(bottom = if(isMobilePOS) 30.dp else 0.dp)
    ) {
        // Sub Total
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Sub Total",
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            Text(
                text = "£${order.net()?.transformDecimal() ?: "0.00"}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )
        }

        // Service Charge
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Service Charge (${productsScreenViewModel.prefs.storeConfigurations?.data?.serviceChargePercentage?.transformDecimal1() ?: 0.0}%)",
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            Text(
                text = "£${serviceCharge.transformDecimal()}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )
        }

        // Discount
        if (state.isDiscountApplied() || true) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Discount (${state.getAppliedDiscountPercentage().transformDecimal()}%)",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color.Red,
                    fontFamily = fontPoppins
                )
                Text(
                    text = "-£${discountAmount.transformDecimal()}",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Red,
                    fontFamily = fontPoppins
                )
            }
        }

        // Total Payable with green background
        androidx.compose.material3.Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF2E7D32)
            ),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Total Payable",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
                Text(
                    text = "£${order.totalPrice(
                        applyServiceCharge = state.isServiceChargeApplied(),
                        serviceChargePercentage = productsScreenViewModel.prefs.storeConfigurations?.data?.serviceChargePercentage ?: 0.0
                    )?.transformDecimal() ?: "0.00"}",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
            }
        }
    }
}

val BillOptionsSize = 380.dp