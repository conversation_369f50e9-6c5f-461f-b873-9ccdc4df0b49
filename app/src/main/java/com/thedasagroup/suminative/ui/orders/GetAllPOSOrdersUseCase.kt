package com.thedasagroup.suminative.ui.orders

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.pagination.GetAllPOSOrdersRequest
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.request.pagination.POSOrderResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import kotlinx.coroutines.flow.StateFlow

open class GetAllPOSOrdersUseCase(
    private val repo: OrdersRepository,
    private val prefs: Prefs
) {
    suspend operator fun invoke(pageNumber: Int, pageSize: Int): StateFlow<Async<POSOrderResponse>> {
        val orderResponse = repo.getAllPOSOrders(
            request = GetAllPOSOrdersRequest(
                storeId = prefs.store?.id ?: 0,
                pageNumber = pageNumber,
                pageSize = pageSize
            )
        )

        return orderResponse
    }
}

