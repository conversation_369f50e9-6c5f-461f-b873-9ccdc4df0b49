package com.thedasagroup.suminative.ui.print

import android.annotation.SuppressLint
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.LinearLayout
import android.widget.ScrollView
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.sales.AutoResizeText
import com.thedasagroup.suminative.ui.sales.FontSizeRange
//import com.thedasagroup.suminative.ui.utils.SunmiPrintHelper
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlin.math.max

@Composable
fun PrintingBill2(orderItem: OrderItem2, prefs: Prefs) {
    val topSpacingMainItems = 8.dp
    val order = orderItem.order ?: Order2()
    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Text(
            text = prefs.loginResponse?.businesses?.name ?: "",
            style = MaterialTheme.typography.h4
        )
    }
    Spacer(modifier = Modifier.padding(8.dp))

    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Text(
            text = "In-Store  -  #${order?.id.toString() ?: ""}",
            style = MaterialTheme.typography.h4,
            color = Color.Black
        )
    }
    if(prefs.storeConfigurations?.data?.vatNumber?.isNotEmpty() == true) {
        Spacer(modifier = Modifier.padding(8.dp))

        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
            Text(
                text = "VAT Number: ${prefs.storeConfigurations?.data?.vatNumber ?: ""}",
                style = MaterialTheme.typography.h6,
                color = Color.Black
            )
        }
    }

    /*Text(
        text = if (order.deliveryType == 2) "Delivery" else "Pick Up",
        style = MaterialTheme.typography.bodySmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    MyTextDivider()*/
    /*Spacer(modifier = Modifier.padding(4.dp))
    Text(
        text = "Customer note",
        style = MaterialTheme.typography.bodySmall,
        color = Color.Black
    )

    Text(text = order.deliveryNote ?: "", style = MaterialTheme.typography.bodySmall)*/
//    Spacer(modifier = Modifier.padding(4.dp))
//    MyTextDivider()
    Spacer(modifier = Modifier.padding(8.dp))
    Text(text = "${order.getCart()?.size ?: 0} Items", style = MaterialTheme.typography.h4)
    Spacer(modifier = Modifier.padding(8.dp))
    val mapItems = order.getCart().groupBy {
        it.storeItem?.categoryId
    }
    mapItems?.forEach { (key, listItems) ->
        Category(
            title = listItems.firstOrNull()?.storeItem?.categoryName ?: "",
            value = "",
            isBold = true,
            style = MaterialTheme.typography.h4
        )
        listItems.forEach { item ->
            Total(
                title = "☐ ${item.storeItem?.quantity} x ${item.storeItem?.name}",
                value = "£${((item.storeItem?.price ?: 0.0) * (item.storeItem?.quantity ?: 0)).transformDecimal()}",
                isBold = true,
                style = MaterialTheme.typography.h4
            )
            Spacer(modifier = Modifier.padding(4.dp))
            item.storeItem?.extras?.forEach { element ->
                Total(
                    title = "☐ ${element.quantity} x ${element.name}",
                    value = "£${((item.storeItem.price ?: 0.0) * (element.quantity ?: 0)).transformDecimal()}",
                )
                Spacer(modifier = Modifier.padding(4.dp))
            }
            item.storeItem?.optionSets?.forEach { element ->
                element.options?.forEach { option ->
                    if ((option?.quantity ?: 0) > 0) {
                        Total(
                            title = "☐ ${option?.quantity} x ${option?.name}",
                            value = "£${((option?.price ?: 0.0) * (option?.quantity ?: 0) * (item.storeItem.quantity ?: 0)).transformDecimal()}"
                        )
                        Spacer(modifier = Modifier.padding(4.dp))
                    }
                }
            }
            if(item.orderNotes?.isNotEmpty() == true){
                OrderNotes(
                    title = "Notes:",
                    value = item.orderNotes,
                    style = MaterialTheme.typography.h4
                )
                Spacer(modifier = Modifier.padding(4.dp))
            }
        }
        Spacer(modifier = Modifier.padding(8.dp))
    }
    MyTextDivider()
    Spacer(modifier = Modifier.padding(8.dp))
    Total(
        title = "Sub Total",
        value = "£${(order.totalPrice ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.h4
    )
    Spacer(modifier = Modifier.padding(8.dp))
    Total(
        title = "Tax",
        value = "£${(order.tax ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.h4
    )
    Spacer(modifier = Modifier.padding(8.dp))
    Total(
        title = "Service Charge",
        value = "£${(order.serviceCharges ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.h4
    )
    Spacer(modifier = Modifier.padding(8.dp))
    Total(
        title = "Discount",
        value = "£${(order.totalDiscount ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.h4
    )
    Spacer(modifier = Modifier.padding(8.dp))
    Total(
        title = "Total",
        value = "£${(order.netPayable ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.h4
    )

//    MyTextDivider()
//    DateComposable(
//        title = "Time Placed",
//        value = "${order.createdOn?.formatDatePrint(DATE_FORMAT_BACK_END2)}",
//        style = MaterialTheme.typography.h4
//    )
    Spacer(modifier = Modifier.padding(8.dp))
    /*if (order.deliveryType != 2) {
        DateComposable(
            title = "Picked Up",
            value = "${order.pickupTime}",
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer(modifier = Modifier.padding(4.dp))
    }*/
    MyTextDivider()

}

@Composable
fun MyTextDivider() {
    Text(
        maxLines = 1,
        text = "--------------------------------------------------------------------------------",
        style = MaterialTheme.typography.h4
    )
}

@Composable
fun Total(
    title: String,
    value: String,
    style: TextStyle = MaterialTheme.typography.h4,
    isBold: Boolean = false
) {
    val minSize = 12.sp
    val maxSize = 25.sp

    val minSize2 = 12.sp
    val maxSize2 = 20.sp
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(end = 10.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Bottom
    ) {
        AutoResizeText(
            modifier = Modifier.width(250.dp),
            text = title,
            style = style,
            fontSizeRange = FontSizeRange(min = minSize, max = maxSize),
            fontWeight = FontWeight.Bold
        )
        AutoResizeText(
            modifier = Modifier
                .width(80.dp)
                .align(Alignment.Bottom), text = value, style = style,
            textAlign = TextAlign.End,
            fontSizeRange = FontSizeRange(min = minSize2, max = maxSize2),
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun Category(
    title: String,
    value: String,
    style: TextStyle = MaterialTheme.typography.h4,
    isBold: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
    ) {
        Text(
            modifier = Modifier.width(270.dp), text = title, style = TextStyle(
                fontStyle = FontStyle.Italic,
                fontSize = 30.sp
            ), fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun TotalCart(
    title: String,
    value: String,
    style: TextStyle = MaterialTheme.typography.h4,
    isBold: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            modifier = Modifier.width(120.sdp),
            text = title,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
        Text(
            modifier = Modifier.width(40.sdp),
            text = value,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
fun DateComposable(
    title: String,
    value: String,
    style: TextStyle = MaterialTheme.typography.h6,
    isBold: Boolean = false
) {
    val minSize = 12.sp
    val maxSize = 18.sp
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        AutoResizeText(
            modifier = Modifier,
            text = title,
            style = style,
            fontSizeRange = FontSizeRange(min = minSize, max = maxSize),
            fontWeight = FontWeight.Bold
        )
        AutoResizeText(
            modifier = Modifier,
            text = value,
            style = style,
            fontSizeRange = FontSizeRange(min = minSize, max = maxSize),
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun OrderNotes(
    title: String,
    value: String,
    style: TextStyle = MaterialTheme.typography.h6,
    isBold: Boolean = false
) {
    Row (
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
//        Text(
//            text = title,
//            style = style,
//            fontSize = 25.sp,
//            fontWeight = FontWeight.Bold,
//            maxLines = 1
//        )
//        Spacer(modifier = Modifier.padding(5.dp))
        Text(
            text = "$title $value",
            style = TextStyle(
                fontStyle = FontStyle.Italic,
                fontSize = 30.sp
            ),
            fontSize = 30.sp,
            fontWeight = FontWeight.Bold,
            overflow = TextOverflow.Ellipsis
        )
    }
}

/*@Composable
fun DateComposable(title: String, value: String, style: TextStyle = MaterialTheme.typography.h6, isBold : Boolean = false) {
    val minSize = 12.sp
    val maxSize = 25.sp
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp)
    ) {
        AutoResizeText(modifier = Modifier.width(150.dp), text = title, style = style, fontSizeRange =  FontSizeRange(min = minSize, max = maxSize),fontWeight = FontWeight.Bold)
        Spacer(modifier = Modifier.width(10.dp))
        AutoResizeText(modifier = Modifier.width(160.dp), textAlign = TextAlign.End,text = value, style = style, fontSizeRange =  FontSizeRange(min = minSize, max = maxSize), fontWeight = FontWeight.Bold)
    }
}*/


@SuppressLint("DefaultLocale")
fun Int.formatOrderId(): String {
    return String.format("%05d", this)
}

//Composeable preview
/*@Composable
@Preview
fun PrintingBillPreview() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color.White)
    ) {
        PrintingBill(
            orderItem = Order(
                customer = Customer(
                    name = "Sheeraz"
                ), id = 1,
            ),
            orderScreenViewModel = mavericksViewModel())
    }
}*/

fun Modifier.verticalScrollbar(
    scrollState: ScrollState,
    scrollBarWidth: Dp = 4.dp,
    minScrollBarHeight: Dp = 5.dp,
    scrollBarColor: Color = Color.Blue,
    cornerRadius: Dp = 2.dp
): Modifier = composed {
    val targetAlpha = if (scrollState.isScrollInProgress) 1f else 0f
    val duration = if (scrollState.isScrollInProgress) 150 else 500

    val alpha by animateFloatAsState(
        targetValue = targetAlpha,
        animationSpec = tween(durationMillis = duration)
    )

    drawWithContent {
        drawContent()

        val needDrawScrollbar = scrollState.isScrollInProgress || alpha > 0.0f

        if (needDrawScrollbar && scrollState.maxValue > 0) {
            val visibleHeight: Float = this.size.height - scrollState.maxValue
            val scrollBarHeight: Float =
                max(visibleHeight * (visibleHeight / this.size.height), minScrollBarHeight.toPx())
            val scrollPercent: Float = scrollState.value.toFloat() / scrollState.maxValue
            val scrollBarOffsetY: Float =
                scrollState.value + (visibleHeight - scrollBarHeight) * scrollPercent

            drawRoundRect(
                color = scrollBarColor,
                topLeft = Offset(this.size.width - scrollBarWidth.toPx(), scrollBarOffsetY),
                size = Size(scrollBarWidth.toPx(), scrollBarHeight),
                alpha = alpha,
                cornerRadius = CornerRadius(cornerRadius.toPx())
            )
        }
    }
}

@Composable
fun DrawScrollableView(content: @Composable () -> Unit, modifier: Modifier) {
    AndroidView(
        modifier = modifier,
        factory = {
            val scrollView = ScrollView(it)
            val layout = LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT)
            scrollView.layoutParams = layout
            scrollView.isVerticalFadingEdgeEnabled = true
            scrollView.isScrollbarFadingEnabled = false
            scrollView.addView(ComposeView(it).apply {
                setContent {
                    content()
                }
            })
            val linearLayout = LinearLayout(it)
            linearLayout.orientation = LinearLayout.VERTICAL
            linearLayout.layoutParams = LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT)
            linearLayout.addView(scrollView)
            linearLayout
        }
    )
}