package com.thedasagroup.suminative.ui.pos_orders

import android.app.ProgressDialog
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.print.PrintBillRequest
import com.thedasagroup.suminative.data.model.response.store_orders.convertCartsToJson
import com.thedasagroup.suminative.printOrderBitmap
import com.thedasagroup.suminative.ui.common.CommonState
import com.thedasagroup.suminative.ui.common.CommonViewModel
import com.thedasagroup.suminative.ui.common.SuccessDialog
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@AndroidEntryPoint
class POSOrdersActivity : AppCompatActivity(), MavericksView {
    val viewModel: POSOrdersViewModel by viewModel()
    val productsScreenViewModel: ProductsScreenViewModel by viewModel()
    val commonViewModel: CommonViewModel by viewModel()
    lateinit var progressDialog : ProgressDialog
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        progressDialog = ProgressDialog(this)
        setContent {
            SumiNativeTheme {
                val coroutineScope = rememberCoroutineScope()
                val showSuccessDialog by commonViewModel.collectAsState(CommonState::showSuccessDialog)
                val successDialogMessage by commonViewModel.collectAsState(CommonState::successDialogMessage)
                POSOrdersScreen(
                    onBackPressed = { finish() },
                    viewModel = viewModel,
                    productsScreenViewModel = productsScreenViewModel,
                    onPrintBill = {bitmap, orderItem2 ->
                        if(productsScreenViewModel.prefs.storeConfigurations?.data?.cloudPrintersEnabledDefault == true){
                            coroutineScope.launch (Dispatchers.IO){
                                withContext(Dispatchers.Main){
                                    progressDialog.setMessage("Printing...")
                                    progressDialog.show()
                                }
                                val cart = orderItem2.order?.getCart()
                                val updatedCart = cart?.map {
                                    it.copy(
                                        orderNumber = orderItem2?.order?.id
                                    )
                                }
                                productsScreenViewModel.printBill(request = PrintBillRequest(
                                    storeId = productsScreenViewModel.prefs.store?.id ?: -1,
                                    cartJson = convertCartsToJson(
                                        carts = updatedCart ?: mutableListOf()
                                    ),
                                    serviceCharge = (orderItem2?.order?.serviceCharges ?: 0.0) > 0.0,
                                    tableName = orderItem2?.tableName ?: ""
                                )
                                ).collectLatest { response ->
                                    when(response){
                                        is Success<*> ->{
                                            withContext(Dispatchers.Main){
                                                commonViewModel.updateShowSuccessDialog(true)
                                                commonViewModel.updateSuccessDialogMessage("Order Printed Successfully")
                                                progressDialog.hide()
                                                viewModel.updateShowPrintingPreview(
                                                    null, shouldPrintInstant = false
                                                )
                                            }
                                        }
                                        is Fail -> {
                                            withContext(Dispatchers.Main) {
                                                commonViewModel.updateShowSuccessDialog(true)
                                                commonViewModel.updateSuccessDialogMessage("Order Printing Failed")
                                                progressDialog.hide()
                                            }
                                        }
                                        is Loading<*> -> {
                                            withContext(Dispatchers.Main) {
                                                progressDialog.show()
                                            }
                                        }
                                        else -> {

                                        }
                                    }
                                }
                            }
                        }
                        else {
                            printOrderBitmap(bitmap, this)
                            viewModel.updateShowPrintingPreview(
                                null, shouldPrintInstant = false
                            )
                        }
                    }
                )

                SuccessDialog(
                    isVisible = showSuccessDialog,
                    message = successDialogMessage,
                    onDismiss = {
                        commonViewModel.updateShowSuccessDialog(false)
                    }
                )
            }
        }
    }

    override fun invalidate() {

    }
}

