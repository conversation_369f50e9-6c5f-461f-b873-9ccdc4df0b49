//package com.thedasagroup.suminative.ui.orders.new_ui
//
//import android.content.Context
//import android.graphics.Bitmap
//import androidx.compose.foundation.BorderStroke
//import androidx.compose.foundation.Image
//import androidx.compose.foundation.background
//import androidx.compose.foundation.clickable
//import androidx.compose.foundation.layout.Arrangement
//import androidx.compose.foundation.layout.Column
//import androidx.compose.foundation.layout.Row
//import androidx.compose.foundation.layout.Spacer
//import androidx.compose.foundation.layout.fillMaxSize
//import androidx.compose.foundation.layout.fillMaxWidth
//import androidx.compose.foundation.layout.height
//import androidx.compose.foundation.layout.padding
//import androidx.compose.foundation.layout.size
//import androidx.compose.foundation.layout.width
//import androidx.compose.foundation.layout.wrapContentSize
//import androidx.compose.foundation.lazy.LazyColumn
//import androidx.compose.foundation.lazy.LazyListScope
//import androidx.compose.foundation.shape.RoundedCornerShape
//import androidx.compose.material.icons.Icons
//import androidx.compose.material.icons.filled.ArrowDownward
//import androidx.compose.material.icons.filled.ArrowForward
//import androidx.compose.material3.Card
//import androidx.compose.material3.Checkbox
//import androidx.compose.material3.CircularProgressIndicator
//import androidx.compose.material3.Divider
//import androidx.compose.material3.Icon
//import androidx.compose.material3.MaterialTheme
//import androidx.compose.material3.Switch
//import androidx.compose.material3.Text
//import androidx.compose.material3.TextButton
//import androidx.compose.runtime.Composable
//import androidx.compose.runtime.getValue
//import androidx.compose.runtime.key
//import androidx.compose.runtime.remember
//import androidx.compose.runtime.rememberCoroutineScope
//import androidx.compose.runtime.toMutableStateMap
//import androidx.compose.ui.Alignment
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.graphics.Color
//import androidx.compose.ui.platform.ComposeView
//import androidx.compose.ui.platform.LocalContext
//import androidx.compose.ui.res.painterResource
//import androidx.compose.ui.text.font.FontWeight
//import androidx.compose.ui.unit.dp
//import androidx.compose.ui.viewinterop.AndroidView
//import androidx.compose.ui.window.Dialog
//import androidx.core.view.drawToBitmap
//import coil.compose.AsyncImage
//import com.airbnb.mvrx.Async
//import com.airbnb.mvrx.Fail
//import com.airbnb.mvrx.Loading
//import com.airbnb.mvrx.Success
//import com.airbnb.mvrx.compose.collectAsState
//import com.thedasagroup.suminative.BuildConfig
//import com.thedasagroup.suminative.R
//import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
//import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
//import com.thedasagroup.suminative.data.model.response.store_orders.Cart
//import com.thedasagroup.suminative.data.model.response.store_orders.Order
//import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel
//import com.thedasagroup.suminative.ui.orders.PrintingPreviewDialog2
//import com.thedasagroup.suminative.ui.service.throttleFirst
//import com.thedasagroup.suminative.ui.theme.PurpleGrey40
//import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
//import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
//import com.thedasagroup.suminative.ui.utils.formatDate
//import com.thedasagroup.suminative.ui.utils.getMinutesBetweenTwoDates
//import com.thedasagroup.suminative.ui.utils.toDate
//import com.thedasagroup.suminative.ui.utils.transformDecimal
//import ir.kaaveh.sdpcompose.sdp
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.flow.collectLatest
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.withContext
//import java.net.InetAddress
//
//
//val buttonWidth = 160.dp
//
//@Composable
//fun OrderScreenTopFunction(
//    viewModel: OrderScreenViewModel,
//    modifier: Modifier,
//    callOrders: () -> Unit,
//    onPrintBill: (Bitmap) -> Unit,
//    onTrackingUrlClick: (String) -> Unit,
//    onUpdateShowAllOrders: (Boolean) -> Unit,
//) {
//    val ordersResponse by viewModel.collectAsState(OrderState::ordersResponse)
//    val isShowAllOrders by viewModel.collectAsState(OrderState::isShowAllOrders)
//
//    ExpandableList2(
//        viewModel = viewModel,
//        modifier = modifier,
//        callOrders = callOrders,
//        onPrintBill = onPrintBill,
//        onTrackingUrlClick = onTrackingUrlClick,
//        onUpdateShowAllOrders = onUpdateShowAllOrders,
//        ordersResponse = ordersResponse,
//        isShowAllOrders = isShowAllOrders,
//        shouldShowAllOrders = true
//    )
//}
//
//@Composable
//fun ExpandableList2(
//    viewModel: OrderScreenViewModel,
//    modifier: Modifier,
//    callOrders: () -> Unit,
//    onPrintBill: (Bitmap) -> Unit,
//    onTrackingUrlClick: (String) -> Unit,
//    onUpdateShowAllOrders: (Boolean) -> Unit,
//    ordersResponse: Async<OrderResponse?>,
//    isShowAllOrders: Boolean,
//    shouldShowAllOrders: Boolean = false
//) {
//    val isShowPrintingPreviewDialog by viewModel.collectAsState(OrderState::isShowPrintingPreview)
//    val isShowChangeStatusDialog by viewModel.collectAsState(OrderState::isShowChangeStatusDialog)
//    val showAcceptOrderDelayDialog by viewModel.collectAsState(OrderState::showAcceptOrderDelayDialog)
//    val currentRouteId by viewModel.collectAsState(OrderState::currentRouteId)
//    val coroutineScope = rememberCoroutineScope()
//
//    if (isShowPrintingPreviewDialog != null) {
//        PrintingPreviewDialog2(order = isShowPrintingPreviewDialog!!, onPrintBill = { bitmap ->
//            onPrintBill(bitmap)
//            viewModel.updateShowPrintingPreview(null, shouldPrintInstant = false)
//        }, onCancel = {
//            viewModel.updateShowPrintingPreview(null, shouldPrintInstant = false)
//        }, orderScreenViewModel = viewModel
//        )
//    }
//
//    if (isShowChangeStatusDialog != null) {
//        viewModel.updateSelectedChangeStatusId(statusId = -1)
//        ChangeStatusDialog(viewModel = viewModel,
//            orderItem = isShowChangeStatusDialog!!,
//            onChangeStatus = {
//                viewModel.updateChangeStatusOrder(order = isShowChangeStatusDialog!!)
//                coroutineScope.launch(Dispatchers.IO) {
//                    viewModel.changeStatusOfOrder(
//                        orderId = isShowChangeStatusDialog!!.order?.id ?: -1,
//                        status = it,
//                        isShowAllOrders = isShowAllOrders
//                    ).collectLatest {
//                        when (it) {
//                            is Success -> {
//                                withContext(Dispatchers.Main) {
//                                    viewModel.updateChangeStatusOrder(order = null)
//                                    viewModel.updateShowChangeStatusDialog(null)
//                                }
//                            }
//
//                            else -> {
//
//                            }
//                        }
//                    }
//                }
//            },
//            onCancel = {
//                viewModel.updateChangeStatusOrder(order = null)
//                viewModel.updateShowChangeStatusDialog(order = null)
//            })
//    } else if (showAcceptOrderDelayDialog != null) {
//        AcceptOrderWithDelayDialog(viewModel = viewModel, onAcceptOrderDelay = { order, delay ->
//            coroutineScope.launch(Dispatchers.IO) {
//                if(order.order?.isScheduled == false) {
//                    viewModel.updateShowPrintingPreview(
//                        order = order, shouldPrintInstant = true
//                    )
//                }
//                viewModel.updateChangeStatusOrder(order = order)
//                viewModel.acceptOrderWithDelay(
//                    orderId = showAcceptOrderDelayDialog!!.order?.id ?: -1,
//                    delayInMinutes = delay,
//                    isShowAllOrders = isShowAllOrders
//                ).collectLatest {
//                    when (it) {
//                        is Success -> {
//                            withContext(Dispatchers.Main) {
//                                viewModel.updateShowAcceptOrderDelayDialog(order = null)
//                                viewModel.updateAcceptOrderDelay(delay = 0)
//                                viewModel.updateShowPrintingPreview(
//                                    order = null, shouldPrintInstant = false
//                                )
//                            }
//                        }
//
//                        else -> {
//
//                        }
//                    }
//                }
//            }
//        }, onCancel = {
//            viewModel.updateShowAcceptOrderDelayDialog(order = null)
//            viewModel.updateAcceptOrderDelay(delay = 0)
//        }, orderItem = showAcceptOrderDelayDialog!!)
//    }
//
//    when (ordersResponse) {
//        is Loading -> {
//            Column(
//                modifier = Modifier.fillMaxSize(),
//                horizontalAlignment = Alignment.CenterHorizontally,
//                verticalArrangement = Arrangement.Center
//            ) {
//                CircularProgressIndicator(color = Color.Blue)
//            }
//        }
//
//        is Success -> {
//            if (ordersResponse()?.success == true && ordersResponse()?.orders?.isNotEmpty() != true) {
//                Column(
//                    modifier = Modifier.fillMaxSize(),
//                    horizontalAlignment = Alignment.CenterHorizontally,
//                    verticalArrangement = Arrangement.Center
//                ) {
//                    CloseStoreComposable(viewModel = viewModel) {
//                        coroutineScope.launch(Dispatchers.IO) {
//                            viewModel.updateIsStoreClosedManual(!it).collectLatest { state ->
//                                when (state) {
//                                    is Success -> {
//
//                                    }
//
//                                    else -> {
//
//                                    }
//                                }
//                            }
//                        }
//                    }
//                    Column(
//                        modifier = Modifier.size(400.dp),
//                        horizontalAlignment = Alignment.CenterHorizontally
//                    ) {
//                        Row(
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .padding(10.dp),
//                            horizontalArrangement = Arrangement.SpaceBetween
//                        ) {
//                            if(shouldShowAllOrders) {
//                                Row(
//                                    modifier = Modifier.height(50.dp),
//                                    verticalAlignment = Alignment.CenterVertically
//                                ) {
//                                    Text(
//                                        text = "Show All Orders",
//                                        style = MaterialTheme.typography.bodyMedium
//                                    )
//                                    Spacer(modifier = Modifier.width(5.dp))
//                                    Switch(modifier = Modifier.size(50.dp),
//                                        checked = isShowAllOrders,
//                                        onCheckedChange = {
//                                            onUpdateShowAllOrders(!isShowAllOrders)
//                                        })
//                                }
//                            }
//                            Spacer(modifier = Modifier.height(10.dp))
//                            TextButton(
//                                onClick = {
//                                    coroutineScope.launch(Dispatchers.IO) {
//
//                                    }
//                                },
//                                modifier = Modifier
//                                    .background(color = Color(0xFF009551))
//                                    .width(100.dp)
//                            ) {
//                                Text(text = "Refresh", color = Color.White)
//                            }
//                        }
//                        Spacer(modifier = Modifier.height(10.dp))
//                        Image(
//                            modifier = Modifier.size(150.dp),
//                            painter = painterResource(id = R.drawable.buy_some_items),
//                            contentDescription = null
//                        )
//                        Spacer(modifier = Modifier.height(20.dp))
//                        Text(
//                            text = "No Orders", style = MaterialTheme.typography.titleLarge
//                        )
//                    }
//                }
//            } else if (ordersResponse()?.success == true) {
//                Column(modifier = modifier.fillMaxSize()) {
//                    CloseStoreComposable(viewModel = viewModel) {
//                        coroutineScope.launch(Dispatchers.IO) {
//                            viewModel.updateIsStoreClosedManual(!it).collectLatest { state ->
//                                when (state) {
//                                    is Success -> {
//
//                                    }
//
//                                    else -> {
//
//                                    }
//                                }
//                            }
//                        }
//                    }
//                    OrderScreen(modifier = modifier,
//                        ordersResponse = ordersResponse() ?: OrderResponse(),
//                        viewModel = viewModel,
//                        onPrintBill = { order ->
//                            if(order.order?.isScheduled == false) {
//                                viewModel.updateShowPrintingPreview(order = order)
//                            }
//                        },
//                        callOrders = {
//                            coroutineScope.launch(Dispatchers.IO) {
//                                getCurrentRouteOrders(currentRouteId = currentRouteId, viewModel = viewModel, isShowAllOrders = isShowAllOrders)
//                            }
//                        },
//                        onUpdateShowAllOrders = {
//                            onUpdateShowAllOrders(it)
//                        },
//                        onChangeStatus = {
//                            viewModel.updateShowChangeStatusDialog(order = it)
//                        },
//                        onUpdateStatusSilent = { order, status, shouldPrint ->
//                            if (shouldPrint && order.order?.isScheduled == false) {
//                                viewModel.updateShowPrintingPreview(
//                                    order = order, shouldPrintInstant = true
//                                )
//                            }
//                            coroutineScope.launch(Dispatchers.IO) {
//                                viewModel.updateClickedOrderId(order.order?.id ?: -1)
//                                viewModel.changeStatusOfOrder(
//                                    orderId = order.order?.id ?: -1,
//                                    status = status,
//                                    isShowAllOrders = isShowAllOrders
//                                ).collectLatest {
//                                    when (it) {
//                                        is Success -> {
//                                            withContext(Dispatchers.Main) {
//                                                viewModel.updateShowPrintingPreview(
//                                                    order = null, shouldPrintInstant = false
//                                                )
//                                                viewModel.updateClickedOrderId(-1)
//                                            }
//                                        }
//
//                                        else -> {
//
//                                        }
//                                    }
//                                }
//                            }
//                        },
//                        onTrackingUrlClick = onTrackingUrlClick,
//                        onAcceptOrderWithDelayDialog = { order ->
//                            viewModel.updateShowAcceptOrderDelayDialog(order = order)
//                        },
//                        updateOrderMinutes = { order ->
//
//                        },
//                        shouldShowAllOrders = shouldShowAllOrders)
//                }
//            } else {
//                Column(
//                    modifier = Modifier.fillMaxSize(),
//                    horizontalAlignment = Alignment.CenterHorizontally,
//                    verticalArrangement = Arrangement.Center
//                ) {
//                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
//                        Text(
//                            text = if (!isNetworkConnected(LocalContext.current)) "No Internet Connection" else "Orders Load Failed",
//                            style = MaterialTheme.typography.bodyLarge
//                        )
//                        Spacer(modifier = Modifier.height(10.dp))
//                        TextButton(
//                            onClick = {
//                                coroutineScope.launch(Dispatchers.IO) {
//                                    getCurrentRouteOrders(currentRouteId, viewModel, isShowAllOrders)
//                                }
//                            },
//                            modifier = Modifier
//                                .background(color = Color(0xFF009551))
//                                .width(100.dp)
//                        ) {
//                            Text(text = "Retry", color = Color.White)
//                        }
//                    }
//
//                }
//            }
//        }
//
//        is Fail -> {
//            Column(
//                modifier = Modifier.fillMaxSize(),
//                horizontalAlignment = Alignment.CenterHorizontally,
//                verticalArrangement = Arrangement.Center
//            ) {
//                Column(horizontalAlignment = Alignment.CenterHorizontally) {
//                    Text(
//                        text = if (!isNetworkConnected(LocalContext.current)) "No Internet Connection" else "Orders Load Failed",
//                        style = MaterialTheme.typography.bodyLarge
//                    )
//                    TextButton(
//                        onClick = {
//                            coroutineScope.launch(Dispatchers.IO) {
//                                getCurrentRouteOrders(currentRouteId, viewModel, isShowAllOrders)
//                            }
//                        }, modifier = Modifier
//                            .background(color = Color(0xFF009551))
//                            .width(100.dp)
//                    ) {
//                        Text(text = "Retry", color = Color.White)
//                    }
//                }
//
//            }
//        }
//
//        else -> {
//
//        }
//    }
//
//}
//
//suspend fun getCurrentRouteOrders(currentRouteId: String, viewModel: OrderScreenViewModel, isShowAllOrders: Boolean) {
//    if (currentRouteId == "0") {
//        viewModel.getOrders(isShowAllOrders = isShowAllOrders)
//    } else {
//        viewModel.getScheduleOrders()
//    }
//}
//
//@Composable
//fun CloseStoreComposable(
//    viewModel: OrderScreenViewModel, onCloseStoreCheckChanged: (Boolean) -> Unit
//) {
//    val closeStoreSettings by viewModel.collectAsState(OrderState::storeCloseSettings)
//    val closedStoreManualResponse by viewModel.collectAsState(OrderState::closedStoreManualResponse)
//    val openingTime by viewModel.collectAsState(OrderState::openingTime)
//    val closingTime by viewModel.collectAsState(OrderState::closeTime)
//    val isStoreClosed = if (closeStoreSettings == "store_close") {
//        true
//    } else {
//        if (closedStoreManualResponse is Success) {
//            closedStoreManualResponse()?.isClosed ?: false
//        } else false
//    }
//
//    val textColor = if (closeStoreSettings != "store_close") {
//        if (closedStoreManualResponse is Success) {
//            if (closedStoreManualResponse()?.isClosed == true) {
//                Color.White
//            } else Color.Black
//        } else Color.White
//    } else Color.White
//
//    Column(
//        modifier = Modifier
//            .height(if (closeStoreSettings == "store_close") 80.sdp else 50.sdp)
//            .fillMaxWidth()
//            .background(
//                color = if (closeStoreSettings != "store_close") {
//                    if (closedStoreManualResponse is Success) {
//                        if (closedStoreManualResponse()?.isClosed == true) {
//                            Color.Red
//                        } else Color.Green
//                    } else Color.Gray
//                } else Color.Red
//            ),
//        horizontalAlignment = Alignment.CenterHorizontally,
//        verticalArrangement = Arrangement.Center
//    ) {
//        if (closedStoreManualResponse is Loading) {
//            CircularProgressIndicator(color = Color.White)
//        } else {
//            Row(
//                modifier = Modifier, verticalAlignment = Alignment.CenterVertically
//            ) {
//                Text(
//                    modifier = Modifier.padding(5.sdp),
//                    text = if (isStoreClosed) "Store Closed" else "Store Open",
//                    style = MaterialTheme.typography.bodyLarge,
//                    color = textColor
//                )
//                Spacer(modifier = Modifier.width(10.sdp))
//                Switch(modifier = Modifier.size(30.sdp),
//                    checked = if (closeStoreSettings != "store_close") {
//                        if (closedStoreManualResponse is Success) {
//                            closedStoreManualResponse()?.isClosed != true
//                        } else false
//                    } else false,
//                    enabled = closeStoreSettings != "store_close",
//                    onCheckedChange = {
//                        onCloseStoreCheckChanged(it)
//                    })
//            }
//            if (closeStoreSettings == "store_close") {
//                Text(
//                    modifier = Modifier.padding(5.sdp),
//                    text = "Store Timings : $openingTime - $closingTime",
//                    style = MaterialTheme.typography.bodySmall,
//                    color = textColor
//                )
//            }
//        }
//    }
//}
//
//@Composable
//fun SectionHeader(
//    orderItem: OrderItem,
//    isExpanded: Boolean,
//    onHeaderClicked: () -> Unit,
//    onPrintBill: (OrderItem) -> Unit,
//    viewModel: OrderScreenViewModel,
//    onAcceptOrderWithDelayDialog: (OrderItem) -> Unit,
//    onChangeStatus: (OrderItem) -> Unit,
//    onUpdateStatusSilent: (OrderItem, Int, Boolean) -> Unit,
//    onTrackingUrlClick: (String) -> Unit,
//    updateOrderMinutes: () -> Unit
//) {
//    val order = orderItem.order ?: Order()
//    val bgColor = if(order.status == 1) Color(0xFFE7B0B0) else Color.White
//    val changeStatusResponse by viewModel.collectAsState(OrderState::changeStatusResponse)
//    val acceptDeliveryOrderResponse by viewModel.collectAsState(OrderState::acceptDeliveryOrderResponse)
//    val clickedOrderId by viewModel.collectAsState(OrderState::clickedOrderId)
//    val currentRouteId by viewModel.collectAsState(OrderState::currentRouteId)
//    val ordersResponse by viewModel.collectAsState(OrderState::ordersResponse)
//    val mapCount by viewModel.collectAsState(OrderState::mapCount)
//    val coroutineScope = rememberCoroutineScope()
//
//    val orderStatus = getOrderStatusString(
//        status = order.status ?: -1, deliveryType = order.deliveryType ?: 1
//    )
//
//    val showCancel = when (order.status) {
//        1 -> true
//        else -> false
//    }
//    val showAcceptButton = when (order.status) {
//        1 -> true
//        2 -> true
//        6 -> true
//        8 -> true
//        else -> false
//    }
//
//    val orderButtonPickup = when (order.status) {
//        1 -> "Accept Order"
//        2 -> "Ready for Pick Up"
//        8 -> "Picked Up"
//        else -> "Accept Order"
//    }
//
//    // send 5 on cancel
//
//    val orderButtonDelivery = when (order.status) {
//        1 -> "Accept Order"
//        9 -> "Driver on its way"
//        7 -> "Delivered"
//        else -> "Accept Order"
//    }
//
//    val orderStatusUpdate = when (order.status) {
//        1 -> 2
//        2 -> 8
//        8 -> 6
//        else -> 1
//    }
//
//    Row(modifier = Modifier
//        .fillMaxWidth()
//        .background(color = bgColor)
//        .clickable { onHeaderClicked() }
//        .padding(vertical = 8.dp, horizontal = 16.dp)) {
//        Column(
//            modifier = Modifier.weight(4f), horizontalAlignment = Alignment.Start
//        ) {
//            Text(
//                text = orderStatus.uppercase(),
//                style = MaterialTheme.typography.bodyLarge,
//                fontWeight = FontWeight.Bold
//            )
//            Text(
//                text = orderItem.customer?.name ?: "", style = MaterialTheme.typography.bodyMedium
//            )
//            Text(
//                text = orderItem.customer?.phone ?: "", style = MaterialTheme.typography.bodyMedium
//            )
//            Text(
//                text = order.createdOn?.formatDate(DATE_FORMAT_BACK_END) ?: "",
//                style = MaterialTheme.typography.bodyMedium
//            )
//            if(order.isScheduled == true && order.scheduledDateTime?.isNotEmpty() == true){
//                Text(
//                    text = "Scheduled On:${
//                        order.scheduledDateTime.formatDate(
//                            DATE_FORMAT_BACK_END
//                        )
//                    }",
//                    style = MaterialTheme.typography.bodyMedium,
//                    color = Color.Red
//                )
//            }
//            Text(
//                text = "£ ${order.netPayable?.transformDecimal()}",
//                style = MaterialTheme.typography.bodyMedium
//            )
//            Divider(modifier = Modifier.fillMaxWidth(), color = Color.Gray, thickness = 1.dp)
//            Spacer(modifier = Modifier.height(10.dp))
//            if (changeStatusResponse is Loading && clickedOrderId == order.id) {
//                CircularProgressIndicator(color = Color.Blue)
//            } else if (acceptDeliveryOrderResponse is Loading && clickedOrderId == order.id) {
//                CircularProgressIndicator(color = Color.Blue)
//            } else {
//                if (order.deliveryType == 1) {
//                    if (showAcceptButton) {
//                        when (order.status) {
//                            6 -> {
//                                Text(
//                                    text = "Order # ${order.id} - Picked Up", color = Color.Black
//                                )
//                            }
//
//                            1 -> {
//                                TextButton(
//                                    onClick = {
//                                        onUpdateStatusSilent(orderItem, orderStatusUpdate, true)
//                                    },
//                                    modifier = Modifier
//                                        .background(color = Color(0xFF0434F6))
//                                        .width(160.dp)
//                                ) {
//                                    Text(
//                                        text = "Accept Order", color = Color.White
//                                    )
//                                }
//                            }
//
//                            else -> {
//                                TextButton(
//                                    onClick = {
//                                        onUpdateStatusSilent(orderItem, orderStatusUpdate, false)
//                                    },
//                                    modifier = Modifier
//                                        .background(color = Color(0xFF0434F6))
//                                        .width(160.dp)
//                                ) {
//                                    Text(
//                                        text = orderButtonPickup, color = Color.White
//                                    )
//                                }
//                            }
//                        }
//                    }
//                } else {
//                    when (order.status) {
//                        1 -> {
//                            Column {
//                                TextButton(
//                                    onClick = {
//                                        onUpdateStatusSilent(orderItem, orderStatusUpdate, true)
//                                    },
//                                    modifier = Modifier
//                                        .background(color = Color(0xFF0434F6))
//                                        .width(buttonWidth)
//                                ) {
//                                    Text(
//                                        text = "Accept Order", color = Color.White
//                                    )
//                                }
//                                if(currentRouteId == "0" && order.scheduledDateTime?.isNotEmpty() != true) {
//                                    Spacer(modifier = Modifier.height(10.dp))
//                                    TextButton(
//                                        onClick = {
//                                            onAcceptOrderWithDelayDialog(orderItem)
//                                        },
//                                        modifier = Modifier
//                                            .background(color = Color(0xFF0434F6))
//                                            .width(buttonWidth)
//                                    ) {
//                                        Text(
//                                            text = "Accept Order Busy", color = Color.White
//                                        )
//                                    }
//                                }
//                            }
//                        }
//
//                        4 -> {
//                            Column {
//                                val dateFormat = if(order.acceptedDate?.contains("T") == true) DATE_FORMAT_BACK_END else DATE_FORMAT_APP
//                                if (order.acceptedDate?.isNotEmpty() == true && order.acceptedDate.toDate(
//                                        dateFormat).time - viewModel.getCurrentUTC()
//                                        .toDate(DATE_FORMAT_APP).time > 60000
//                                ) {
//                                    Text(text = "Delivery Driver Arriving in")
//                                    Spacer(modifier = Modifier.height(10.dp))
//                                    Text(
//                                        text = "${
//                                            getMinutesBetweenTwoDates(
//                                                viewModel.getCurrentUTC(), order.acceptedDate,
//                                                formatStart = DATE_FORMAT_APP, formatEnd = dateFormat
//                                            )
//                                        } minutes"
//                                    )
//                                }
//                            }
//                        }
//
//                        7 -> {
//                            Text(text = "Order # ${order.id} - Delivery", color = Color.Black)
//                        }
//
//                        else -> {
//
//                        }
//                    }
//                }
//            }
//            Spacer(modifier = Modifier.height(10.dp))
//            if (order.trackingUrl != null && order.status != 7) {
//                TextButton(
//                    onClick = {
//                        onTrackingUrlClick(order.trackingUrl)
//                    }, modifier = Modifier
//                        .background(color = Color(0xFFFFC107))
//                        .width(buttonWidth)
//                ) {
//                    Text(text = "Tracking", color = Color.Black)
//                }
//            }
//            Spacer(modifier = Modifier.height(10.dp))
//            if((order.isScheduled == false && currentRouteId == "0" && BuildConfig.SHOW_PRINTBILL.isNotEmpty())) {
//                TextButton(
//                    onClick = {
//                        onPrintBill(orderItem)
//                    }, modifier = Modifier
//                        .background(color = Color(0xFF009551))
//                        .width(buttonWidth)
//                ) {
//                    Text(text = "Print Bill", color = Color.White)
//                }
//            }
//            Spacer(modifier = Modifier.height(10.dp))
//            if (showCancel) {
//                TextButton(
//                    onClick = {
//                        onUpdateStatusSilent(orderItem, 5, false)
//                    }, modifier = Modifier
//                        .background(color = Color(0xFFE82738))
//                        .width(buttonWidth)
//                ) {
//                    Text(text = "Cancel", color = Color.White)
//                }
//            }
//            Text(
//                text = "Order# ${order.id.toString()}", style = MaterialTheme.typography.bodyMedium
//            )
//            Text(
//                text = if (order.deliveryType == 2) "Delivery" else "Pickup",
//                style = MaterialTheme.typography.bodyMedium
//            )
//        }
//
//        if (isExpanded) {
//            Icon(
//                modifier = Modifier.weight(1f),
//                imageVector = Icons.Default.ArrowDownward,
//                contentDescription = "",
//                tint = Color.Black
//            )
//        } else {
//            Icon(
//                modifier = Modifier.weight(1f),
//                imageVector = Icons.Default.ArrowForward,
//                contentDescription = "",
//                tint = Color.Black
//            )
//        }
//    }
//}
//
//@Composable
//fun ChangeStatusDialog(
//    viewModel: OrderScreenViewModel,
//    modifier: Modifier = Modifier,
//    onChangeStatus: (Int) -> Unit,
//    onCancel: () -> Unit,
//    orderItem: OrderItem
//) {
//    val order = orderItem.order ?: Order()
//    val selectedChangeStatusId by viewModel.collectAsState(OrderState::selectedChangeStatusId)
//    val changeStatusResponse by viewModel.collectAsState(OrderState::acceptDeliveryOrderResponse)
//    val changeStatusOrder by viewModel.collectAsState(OrderState::changeStatusOrder)
//
//    Dialog(onDismissRequest = { onCancel() }) {
//        Card(
//            //shape = MaterialTheme.shapes.medium,
//            shape = RoundedCornerShape(10.dp),
//            // modifier = modifier.size(280.dp, 240.dp)
//            modifier = Modifier.padding(10.dp, 5.dp, 10.dp, 10.dp)
//        ) {
//            if (changeStatusResponse is Loading && order.id == changeStatusOrder?.order?.id) {
//                Column(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .background(color = Color.White)
//                        .padding(8.dp),
//                    horizontalAlignment = Alignment.CenterHorizontally,
//                    verticalArrangement = Arrangement.Center
//                ) {
//                    CircularProgressIndicator(color = Color.Blue)
//                }
//            } else {
//                Column(
//                    modifier.background(Color.White)
//                ) {
//                    Column(
//                        modifier = Modifier
//                            .background(color = Color.White)
//                            .padding(8.dp),
//                        horizontalAlignment = Alignment.CenterHorizontally
//                    ) {
//                        Row(
//                            modifier = Modifier.fillMaxWidth(),
//                            horizontalArrangement = Arrangement.SpaceBetween
//                        ) {
//                            Text(
//                                text = "Accept Order",
//                                style = MaterialTheme.typography.bodyLarge,
//                                fontWeight = FontWeight.Bold
//                            )
//                            Checkbox(checked = selectedChangeStatusId == 2, onCheckedChange = {
//                                val status = if (it) 2 else -1
//                                viewModel.updateSelectedChangeStatusId(status)
//                            })
//                        }
//                        Row(
//                            modifier = Modifier.fillMaxWidth(),
//                            horizontalArrangement = Arrangement.SpaceBetween
//                        ) {
//                            Text(
//                                text = "Driver on its way",
//                                style = MaterialTheme.typography.bodyLarge,
//                                fontWeight = FontWeight.Bold
//                            )
//                            Checkbox(checked = selectedChangeStatusId == 9, onCheckedChange = {
//                                val status = if (it) 9 else -1
//                                viewModel.updateSelectedChangeStatusId(status)
//                            })
//                        }
//                        Row(
//                            modifier = Modifier.fillMaxWidth(),
//                            horizontalArrangement = Arrangement.SpaceBetween
//                        ) {
//                            Text(
//                                text = "Delivered",
//                                style = MaterialTheme.typography.bodyLarge,
//                                fontWeight = FontWeight.Bold
//                            )
//                            Checkbox(checked = selectedChangeStatusId == 7, onCheckedChange = {
//                                val status = if (it) 7 else -1
//                                viewModel.updateSelectedChangeStatusId(status)
//                            })
//                        }
//                    }
//                    Row(
//                        Modifier
//                            .fillMaxWidth()
//                            .padding(top = 10.dp)
//                            .background(Color(0xFF009551)),
//                        horizontalArrangement = Arrangement.SpaceAround
//                    ) {
//
//                        TextButton(onClick = {
//                            onCancel()
//                        }) {
//
//                            Text(
//                                "Cancel",
//                                fontWeight = FontWeight.Bold,
//                                color = PurpleGrey40,
//                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
//                            )
//                        }
//                        TextButton(onClick = {
//                            onChangeStatus(selectedChangeStatusId)
//                        }) {
//                            Text(
//                                "Ok",
//                                fontWeight = FontWeight.ExtraBold,
//                                color = Color.Black,
//                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
//                            )
//                        }
//                    }
//                }
//            }
//        }
//    }
//}
//
//@Composable
//fun AcceptOrderWithDelayDialog(
//    viewModel: OrderScreenViewModel,
//    modifier: Modifier = Modifier,
//    onAcceptOrderDelay: (OrderItem, Int) -> Unit,
//    onCancel: () -> Unit,
//    orderItem: OrderItem
//) {
//    val order = orderItem.order ?: Order()
//    val acceptOrderDelay by viewModel.collectAsState(OrderState::acceptOrderDelay)
//    val changeStatusResponse by viewModel.collectAsState(OrderState::acceptDeliveryOrderResponse)
//    val changeStatusOrder by viewModel.collectAsState(OrderState::changeStatusOrder)
//
//    Dialog(onDismissRequest = { onCancel() }) {
//        Card(
//            //shape = MaterialTheme.shapes.medium,
//            shape = RoundedCornerShape(10.dp),
//            // modifier = modifier.size(280.dp, 240.dp)
//            modifier = Modifier.padding(10.dp, 5.dp, 10.dp, 10.dp)
//        ) {
//            if (changeStatusResponse is Loading && order.id == changeStatusOrder?.order?.id) {
//                Column(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .background(color = Color.White)
//                        .padding(8.dp),
//                    horizontalAlignment = Alignment.CenterHorizontally,
//                    verticalArrangement = Arrangement.Center
//                ) {
//                    CircularProgressIndicator(color = Color.Blue)
//                }
//            } else {
//                Column(
//                    modifier.background(Color.White),
//                    horizontalAlignment = Alignment.CenterHorizontally
//                ) {
//                    Spacer(modifier = Modifier.height(10.dp))
//                    Text(
//                        text = "Accept Order In",
//                        style = MaterialTheme.typography.bodyLarge,
//                        fontWeight = FontWeight.Bold
//                    )
//                    Spacer(modifier = Modifier.height(10.dp))
//                    Column(
//                        modifier = Modifier
//                            .background(color = Color.White)
//                            .padding(8.dp),
//                        horizontalAlignment = Alignment.CenterHorizontally
//                    ) {
//                        Row(
//                            modifier = Modifier.fillMaxWidth(),
//                            horizontalArrangement = Arrangement.SpaceBetween
//                        ) {
//                            Text(
//                                text = "15 minutes",
//                                style = MaterialTheme.typography.bodyLarge,
//                                fontWeight = FontWeight.Bold
//                            )
//                            Checkbox(checked = acceptOrderDelay == 15, onCheckedChange = {
//                                val time = if (it) 15 else 0
//                                viewModel.updateAcceptOrderDelay(delay = time)
//                            })
//                        }
//                        Row(
//                            modifier = Modifier.fillMaxWidth(),
//                            horizontalArrangement = Arrangement.SpaceBetween
//                        ) {
//                            Text(
//                                text = "30 minutes",
//                                style = MaterialTheme.typography.bodyLarge,
//                                fontWeight = FontWeight.Bold
//                            )
//                            Checkbox(checked = acceptOrderDelay == 30, onCheckedChange = {
//                                val time = if (it) 30 else 0
//                                viewModel.updateAcceptOrderDelay(delay = time)
//                            })
//                        }
//                        Row(
//                            modifier = Modifier.fillMaxWidth(),
//                            horizontalArrangement = Arrangement.SpaceBetween
//                        ) {
//                            Text(
//                                text = "35 Minutes",
//                                style = MaterialTheme.typography.bodyLarge,
//                                fontWeight = FontWeight.Bold
//                            )
//                            Checkbox(checked = acceptOrderDelay == 35, onCheckedChange = {
//                                val time = if (it) 35 else 0
//                                viewModel.updateAcceptOrderDelay(delay = time)
//                            })
//                        }
//                    }
//                    Row(
//                        Modifier
//                            .fillMaxWidth()
//                            .padding(top = 10.dp)
//                            .background(Color(0xFF009551)),
//                        horizontalArrangement = Arrangement.SpaceAround
//                    ) {
//
//                        TextButton(onClick = {
//                            onCancel()
//                        }) {
//
//                            Text(
//                                "Cancel",
//                                fontWeight = FontWeight.Bold,
//                                color = PurpleGrey40,
//                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
//                            )
//                        }
//                        TextButton(onClick = {
//                            onAcceptOrderDelay(orderItem, acceptOrderDelay)
//                        }) {
//                            Text(
//                                "Ok",
//                                fontWeight = FontWeight.ExtraBold,
//                                color = Color.Black,
//                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
//                            )
//                        }
//                    }
//                }
//            }
//        }
//    }
//}
//
//@Composable
//fun screenshotableComposable(content: @Composable () -> Unit): () -> Bitmap {
//    val context = LocalContext.current
//    val composeView = remember { ComposeView(context = context) }
//    fun captureBitmap(): Bitmap = composeView.drawToBitmap()
//    AndroidView(
//        factory = {
//            composeView.apply {
//                setContent {
//                    content()/*...content...*/
//                }
//            }
//        },
//        modifier = Modifier.wrapContentSize(unbounded = true)   //  Make sure to set unbounded true to draw beyond screen area
//    )
//
//    return ::captureBitmap
//}
//
//@Composable
//fun OrderScreen(
//    ordersResponse: OrderResponse,
//    modifier: Modifier,
//    viewModel: OrderScreenViewModel,
//    onPrintBill: (OrderItem) -> Unit,
//    callOrders: () -> Unit,
//    onUpdateShowAllOrders: (Boolean) -> Unit,
//    onChangeStatus: (OrderItem) -> Unit,
//    onUpdateStatusSilent: (OrderItem, Int, Boolean) -> Unit,
//    onTrackingUrlClick: (String) -> Unit,
//    onAcceptOrderWithDelayDialog: (OrderItem) -> Unit,
//    updateOrderMinutes: (OrderItem) -> Unit,
//    shouldShowAllOrders: Boolean = false,
//) {
//    val isExpandedMap = remember {
//        List(
//            ordersResponse.orders?.size ?: 0
//        ) { index: Int -> index to false }.toMutableStateMap()
//    }
//
//    val isExpanded by viewModel.collectAsState(OrderState::isExpanded)
//    val isShowAllOrders by viewModel.collectAsState(OrderState::isShowAllOrders)
//    val coroutineScope = rememberCoroutineScope()
//
//    val function = throttleFirst<Unit>(coroutineScope = coroutineScope, skipMs = 2000) {
//        coroutineScope.launch(Dispatchers.IO) {
//            callOrders()
//        }
//    }
//
//    LazyColumn(modifier = Modifier.background(color = Color(0xFFC1C8D0)),
//        horizontalAlignment = Alignment.CenterHorizontally,
//        verticalArrangement = Arrangement.Center,
//        content = {
//            item {
//                Spacer(modifier = Modifier.height(10.dp))
//                Column(
//                    modifier = Modifier.fillMaxWidth(),
//                    horizontalAlignment = Alignment.CenterHorizontally
//                ) {
//                    Text(
//                        text = "Store Orders",
//                        style = MaterialTheme.typography.bodyLarge,
//                        fontWeight = FontWeight.Bold
//                    )
//                    Spacer(modifier = Modifier.height(10.dp))
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .padding(10.dp),
//                        horizontalArrangement = Arrangement.SpaceBetween,
//                        verticalAlignment = Alignment.CenterVertically
//                    ) {
//                        if(shouldShowAllOrders) {
//                            Row(
//                                modifier = Modifier.height(50.dp),
//                                verticalAlignment = Alignment.CenterVertically
//                            ) {
//                                Text(
//                                    text = "Show All Orders",
//                                    style = MaterialTheme.typography.bodyMedium
//                                )
//                                Spacer(modifier = Modifier.width(5.dp))
//                                Switch(modifier = Modifier.size(50.dp),
//                                    checked = isShowAllOrders,
//                                    onCheckedChange = {
//                                        onUpdateShowAllOrders(!isShowAllOrders)
//                                    })
//                            }
//                        }
//                        Spacer(modifier = Modifier.height(10.dp))
//                        TextButton(
//                            onClick = {
//                                function(Unit)
//                            },
//                            modifier = Modifier
//                                .background(color = Color(0xFF009551))
//                                .width(100.dp)
//                        ) {
//                            Text(text = "Refresh", color = Color.White)
//                        }
//                    }
//                }
//            }
//            ordersResponse.orders?.onEachIndexed { index, order ->
//                Section(orderItem = order,
//                    isExpanded = isExpandedMap[index] ?: true,
//                    onHeaderClick = {
//                        isExpandedMap[index] = !(isExpandedMap[index] ?: true)
//                        viewModel.updateExpanded(isExpandedMap[index] ?: true)
//                    },
//                    onPrintBill = onPrintBill,
//                    viewModel = viewModel,
//                    onAcceptOrderWithDelayDialog = onAcceptOrderWithDelayDialog,
//                    onChangeStatus = onChangeStatus,
//                    onUpdateStatusSilent = onUpdateStatusSilent,
//                    onTrackingUrlClick = onTrackingUrlClick,
//                    updateOrderMinutes = {
//                        updateOrderMinutes(order)
//                    })
//            }
//        })
//}
//
//fun LazyListScope.Section(
//    orderItem: OrderItem,
//    isExpanded: Boolean,
//    onHeaderClick: () -> Unit,
//    onPrintBill: (OrderItem) -> Unit,
//    viewModel: OrderScreenViewModel,
//    onAcceptOrderWithDelayDialog: (OrderItem) -> Unit,
//    onChangeStatus: (OrderItem) -> Unit,
//    onUpdateStatusSilent: (OrderItem, Int, Boolean) -> Unit,
//    onTrackingUrlClick: (String) -> Unit,
//    updateOrderMinutes: () -> Unit
//) {
//
//    val order = orderItem.order ?: Order()
//
//    val bgColor = if(order.status == 1) Color(0xFFE7B0B0) else Color.White
//
//    item {
//        key(order.id.toString() + order.status.toString() + order.countMinutes.toString() + order.isScheduled.toString() + order.scheduledDateTime) {
//            Card(border = BorderStroke(1.dp,Color(0xFFC1C8D0)),
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(start = 10.dp, end = 10.dp, top = 10.dp)
//                    .clickable {
//                        onHeaderClick()
//                    }) {
//                SectionHeader(
//                    orderItem = orderItem,
//                    isExpanded = isExpanded,
//                    onHeaderClicked = onHeaderClick,
//                    onPrintBill = onPrintBill,
//                    viewModel = viewModel,
//                    onAcceptOrderWithDelayDialog = onAcceptOrderWithDelayDialog,
//                    onChangeStatus = onChangeStatus,
//                    onUpdateStatusSilent = onUpdateStatusSilent,
//                    onTrackingUrlClick = onTrackingUrlClick,
//                    updateOrderMinutes = updateOrderMinutes
//                )
//            }
//        }
//    }
//
//    if (isExpanded) {
//        item {
//            Card(
//                border = BorderStroke(1.dp, Color(0xFFC1C8D0)),
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(start = 10.dp, end = 10.dp, bottom = 10.dp)
//            ) {
//
//                order.getCart().forEachIndexed { index, cart ->
//                    CartHeader(cart = cart, bgColor = bgColor)
//
//                    if (cart.storeItem?.optionSets?.isNotEmpty() == true) {
//                        Column(
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .background(color = bgColor)
//                        ) {
//                            Text(
//                                modifier = Modifier
//                                    .background(color = bgColor)
//                                    .padding(10.dp),
//                                text = "Options",
//                                style = MaterialTheme.typography.bodyMedium,
//                                fontWeight = FontWeight.Bold
//                            )
//                            Spacer(
//                                modifier = Modifier
//                                    .background(color = bgColor)
//                                    .height(10.dp)
//                            )
//                        }
//                    }
//
//                    val options = cart.storeItem?.optionSets?.flatMap {
//                        it.options ?: mutableListOf()
//                    }
//
//                    options?.forEachIndexed { index, option ->
//                        Column(
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .background(color = bgColor)
//                        ) {
//
//                            if ((option.quantity ?: 0) > 0) {
//                                Row(
//                                    modifier = Modifier
//                                        .fillMaxWidth()
//                                        .background(color = bgColor)
//                                        .padding(start = 10.dp, top = 10.dp, bottom = 10.dp),
//                                    horizontalArrangement = Arrangement.SpaceBetween
//                                ) {
//                                    Text(
//                                        modifier = Modifier
//                                            .weight(2f)
//                                            .padding(5.dp),
//                                        text = option.name ?: "",
//                                        style = MaterialTheme.typography.bodySmall
//                                    )
//                                    Text(
//                                        modifier = Modifier
//                                            .weight(1f)
//                                            .padding(5.dp),
//                                        text = option.quantity.toString(),
//                                        style = MaterialTheme.typography.bodySmall
//                                    )
//                                    Text(
//                                        modifier = Modifier
//                                            .weight(1f)
//                                            .padding(5.dp),
//                                        text = "£ ${((option.price ?: 0.0) * (option.quantity ?: 0) * (cart.storeItem.quantity ?: 0)).transformDecimal()}",
//                                        style = MaterialTheme.typography.bodySmall
//                                    )
//                                }
//                            }
//                            Divider(
//                                modifier = Modifier
//                                    .background(color = bgColor)
//                                    .fillMaxWidth(),
//                                color = Color.Gray,
//                                thickness = 1.dp
//                            )
//                        }
//                    }
//                }
//                Column(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .background(color = bgColor)
//                ) {
//                    Spacer(
//                        modifier = Modifier
//                            .background(color = bgColor)
//                            .height(10.dp)
//                    )
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), horizontalArrangement = Arrangement.SpaceBetween
//                    ) {
//                        Text(
//                            text = "Delivery Type", style = MaterialTheme.typography.bodyMedium
//                        )
//                        Text(
//                            text = if (order.deliveryType == 2) "Delivery" else "Pickup",
//                            style = MaterialTheme.typography.bodyMedium
//                        )
//                    }
//                    Spacer(modifier = Modifier.height(10.dp))
//                    if (order.deliveryType == 1) {
//                        Row(
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .background(color = bgColor)
//                                .padding(10.dp), horizontalArrangement = Arrangement.SpaceBetween
//                        ) {
//                            Text(
//                                text = "Pickup Time", style = MaterialTheme.typography.bodyMedium
//                            )
//                            Text(
//                                text = order.pickupTime ?: "",
//                                style = MaterialTheme.typography.bodyMedium
//                            )
//                        }
//                        Spacer(modifier = Modifier.height(10.dp))
//                    }
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), horizontalArrangement = Arrangement.SpaceBetween
//                    ) {
//                        Text(
//                            text = "Payment Type", style = MaterialTheme.typography.bodyMedium
//                        )
//                        Text(
//                            text = if (order.paymentType == 0) "Online Payment" else "",
//                            style = MaterialTheme.typography.bodyMedium
//                        )
//                    }
//                    Spacer(modifier = Modifier.height(10.dp))
//                    Divider(
//                        modifier = Modifier
//                            .background(color = bgColor)
//                            .fillMaxWidth(),
//                        color = Color.Gray,
//                        thickness = 1.dp
//                    )
//                }
//                Column(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .background(color = bgColor)
//                ) {
//                    Spacer(
//                        modifier = Modifier
//                            .background(color = bgColor)
//                            .height(10.dp)
//                    )
//                    Text(
//                        modifier = Modifier
//                            .background(color = bgColor)
//                            .padding(10.dp),
//                        text = "Bill Details",
//                        style = MaterialTheme.typography.bodyLarge,
//                        fontWeight = FontWeight.Bold
//                    )
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), horizontalArrangement = Arrangement.SpaceBetween
//                    ) {
//                        Text(
//                            text = "Item Totals", style = MaterialTheme.typography.bodyMedium
//                        )
//                        Text(
//                            text = "£ ${order.totalPrice?.transformDecimal()}",
//                            style = MaterialTheme.typography.bodyMedium
//                        )
//                    }
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), horizontalArrangement = Arrangement.SpaceBetween
//                    ) {
//                        Text(
//                            text = "Option Total", style = MaterialTheme.typography.bodyMedium
//                        )
//                        Text(
//                            text = "£ ${order.totalOptionPrice?.transformDecimal()}",
//                            style = MaterialTheme.typography.bodyMedium
//                        )
//                    }
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), horizontalArrangement = Arrangement.SpaceBetween
//                    ) {
//                        Text(
//                            text = "Addons Total", style = MaterialTheme.typography.bodyMedium
//                        )
//                        Text(
//                            text = "£ ${order.totalExtraPrice?.transformDecimal()}",
//                            style = MaterialTheme.typography.bodyMedium
//                        )
//                    }
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), horizontalArrangement = Arrangement.SpaceBetween
//                    ) {
//                        Text(
//                            text = "Delivery Fee:", style = MaterialTheme.typography.bodyMedium
//                        )
//                        Text(
//                            text = "£ ${order.deliveryCharges?.transformDecimal()}",
//                            style = MaterialTheme.typography.bodyMedium
//                        )
//                    }
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), horizontalArrangement = Arrangement.SpaceBetween
//                    ) {
//                        Text(text = "Taxes", style = MaterialTheme.typography.bodyMedium)
//                        Text(
//                            text = "£ ${order.tax?.transformDecimal()}",
//                            style = MaterialTheme.typography.bodyMedium
//                        )
//                    }
//                    Divider(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), color = Color.Gray, thickness = 1.dp
//                    )
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor),
//                        horizontalArrangement = Arrangement.SpaceBetween
//                    ) {
//                        Text(
//                            modifier = Modifier.padding(10.dp),
//                            text = "Total Payable",
//                            style = MaterialTheme.typography.bodyLarge,
//                            fontWeight = FontWeight.Bold
//                        )
//                        Text(
//                            modifier = Modifier.padding(10.dp),
//                            text = "£ ${order.netPayable?.transformDecimal()}",
//                            style = MaterialTheme.typography.bodyLarge,
//                            fontWeight = FontWeight.Bold
//                        )
//                    }
//                    Divider(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .background(color = bgColor)
//                            .padding(10.dp), color = Color.Gray, thickness = 1.dp
//                    )
//                }
//            }
//        }
//    }
//}
//
//@Composable
//fun CartHeader(cart: Cart, bgColor: Color) {
//    Column(
//        modifier = Modifier
//            .fillMaxWidth()
//            .background(color = bgColor),
//        verticalArrangement = Arrangement.Center
//    ) {
//
//        Spacer(modifier = Modifier.height(10.dp))
//
//        Row(
//            modifier = Modifier
//                .fillMaxWidth()
//                .background(color = bgColor),
//            horizontalArrangement = Arrangement.SpaceEvenly
//        ) {
//            AsyncImage(
//                modifier = Modifier
//                    .background(color = bgColor)
//                    .size(50.dp),
//                model = "https://dasasplace.com/dasa/streamer?name=${cart.storeItem?.pic}",
//                contentDescription = "",
//            )
//            Text(
//                modifier = Modifier
//                    .background(color = bgColor)
//                    .width(100.dp),
//                text = cart.storeItem?.name ?: "",
//                style = MaterialTheme.typography.bodyMedium
//            )
//            Text(
//                modifier = Modifier.background(color = bgColor),
//                text = cart.storeItem?.quantity.toString() ?: "",
//                style = MaterialTheme.typography.bodyMedium
//            )
//            Column(modifier = Modifier.background(color = bgColor)) {
//                Text(
//                    text = "£ ${cart.storeItem?.price?.transformDecimal().toString()}",
//                    style = MaterialTheme.typography.bodyMedium
//                )/*Text(
//                    text = cart.storeItem?.price?.transformDecimal().toString(),
//                    style = MaterialTheme.typography.bodyMedium
//                )*/
//            }
//        }
//        Spacer(
//            modifier = Modifier
//                .background(color = bgColor)
//                .height(10.dp)
//        )
//    }
//}
//
//fun getOrderStatusString(status: Int, deliveryType: Int): String {
//    if (deliveryType == 2) {
//        return when (status) {
//            1 -> "Pending"
//            2 -> "Accepted"
//            3 -> "Rejected"
//            4 -> "Accepted, Processing Delivery"
//            5 -> "Canceled"
//            6 -> "Picked Up"
//            7 -> "Delivered"
//            8 -> "Ready for Pick Up"
//            9 -> "Out for Delivery"
//            10 -> "Courier Assigned"
//            11 -> "Searching for Courier"
//            12 -> "Courier Assigned"
//            13 -> "Courier Arriving to Pickup Food"
//            14 -> "Customer Drop Off Imminent"
//            24 -> "Accepted, Scheduled for Delivery"
//            25 -> "Canceled by Vendor"
//            26 -> "Canceled by Customer"
//            else -> "Pending"
//        }
//    } else {
//        return when (status) {
//            1 -> "Pending"
//            2 -> "Accepted"
//            3 -> "Rejected"
//            4 -> "Processing"
//            5 -> "Canceled"
//            6 -> "Picked Up"
//            7 -> "Delivered"
//            8 -> "Ready for Pick Up"
//            9 -> "Driver on its way"
//            10 -> "Courier Assigned"
//            11 -> "Searching for Courier"
//            12 -> "Courier Reassigning"
//            24 -> "Accepted, Scheduled for Delivery"
//            25 -> "Canceled by Vendor"
//            26 -> "Canceled by Customer"
//            else -> "Pending"
//        }
//    }
//}
//
//
//fun isNetworkConnected(context: Context): Boolean {
//    try {
//        val ipAddr = InetAddress.getByName("google.com")
//
//        //You can replace it with your name
//        return ipAddr.toString() != ""
//    } catch (e: Exception) {
//        return false
//    }
//}
