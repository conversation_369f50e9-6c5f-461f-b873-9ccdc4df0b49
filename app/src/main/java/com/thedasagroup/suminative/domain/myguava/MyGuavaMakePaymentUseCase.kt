package com.thedasagroup.suminative.domain.myguava

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.my_guava.orders.CreateOrderRequest
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder
import com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.formatDate
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.UUID

open class MyGuavaMakePaymentUseCase(
    private val createOrderUseCase: MyGuavaCreateOrderUseCase,
    private val getTerminalsUseCase: MyGuavaGetTerminalsUseCase,
    private val createSessionUseCase: MyGuavaCreateSessionUseCase
) {
    suspend operator fun invoke(order: Order, terminal: Terminal): StateFlow<Async<Session>> {
        val guavaOrderResponse = createOrderUseCase(order = order)
        val createSessionResponse = MutableStateFlow<Async<Session>>(Loading())
        when(guavaOrderResponse.value){
            is Success ->{
                when (guavaOrderResponse.value) {
                    is Success -> {
                        val guavaOrder =
                            (guavaOrderResponse.value as Success).invoke().data ?: GuavaOrder()
                        val sessionResponse = createSessionUseCase(guavaOrder, terminal)
                        return when (sessionResponse.value) {
                            is Success -> {
                                sessionResponse
                            }

                            is Fail -> {
                                MutableStateFlow(Fail(error = Throwable((sessionResponse.value as Fail<*>).error.message)))
                            }

                            else -> {
                                sessionResponse
                            }
                        }
                    }
                    is Fail -> {
                        return MutableStateFlow(Fail(error = Throwable((guavaOrderResponse.value as Fail<*>).error.message)))
                    }
                    else -> {
                        return createSessionResponse
                    }
                }
            }
            else -> {
            }
        }
        return createSessionResponse
    }
}
