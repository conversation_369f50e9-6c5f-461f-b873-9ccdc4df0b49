package com.thedasagroup.suminative.domain.void_notification

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.print.VoidNotificationRequest
import com.thedasagroup.suminative.data.model.response.void_notification.VoidNotificationResponse
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import kotlinx.coroutines.flow.StateFlow
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject

/**
 * Use case for sending void notification to printer
 */
class SendVoidNotificationUseCase @Inject constructor(
    private val reservationsRepository: ReservationsRepository
) {
    
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    /**
     * Send void notification using request object
     */
    suspend operator fun invoke(
        request: VoidNotificationRequest
    ): StateFlow<Async<VoidNotificationResponse>> {
        return reservationsRepository.sendVoidNotification(request)
    }
    
    /**
     * Send void notification using individual parameters
     */
    suspend operator fun invoke(
        storeId: Int,
        courseName: String,
        tableName: String,
        cartJson: String
    ): StateFlow<Async<VoidNotificationResponse>> {
        return reservationsRepository.sendVoidNotification(
            storeId = storeId,
            courseName = courseName,
            tableName = tableName,
            cartJson = cartJson
        )
    }
    
    /**
     * Send void notification using Cart list
     */
    suspend operator fun invoke(
        storeId: Int,
        courseName: String,
        tableName: String,
        carts: List<Cart>
    ): StateFlow<Async<VoidNotificationResponse>> {
        val cartJson = json.encodeToString(carts)
        return reservationsRepository.sendVoidNotification(
            storeId = storeId,
            courseName = courseName,
            tableName = tableName,
            cartJson = cartJson
        )
    }
}

