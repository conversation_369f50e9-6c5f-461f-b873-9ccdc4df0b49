package com.thedasagroup.suminative.data.model.response.void_notification

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Response model for void notification API
 * Endpoint: /api/printer/voidNotification
 */
@Serializable
data class VoidNotificationResponse(
    @SerialName("success")
    val success: Boolean = false,
    @SerialName("message")
    val message: String? = null,
    @SerialName("data")
    val data: String? = null,
    @SerialName("statusCode")
    val statusCode: Int? = null
)

