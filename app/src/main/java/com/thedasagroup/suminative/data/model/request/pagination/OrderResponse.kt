package com.thedasagroup.suminative.data.model.request.pagination

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OrderResponse(
    @SerialName("orders")
    val orders : List<OrderItem>? = emptyList(),
    @SerialName("totalCount")
    val totalCount : Int = 0,
    val success: Boolean = false,
)


@Serializable
data class POSOrderResponse(
    @SerialName("orders")
    val orders : List<OrderItem2>? = emptyList(),
    @SerialName("totalCount")
    val totalCount : Int = 0,
    val success: Boolean = false,
)