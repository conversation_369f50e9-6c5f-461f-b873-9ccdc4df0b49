package com.thedasagroup.suminative.data.model.response.reservations

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * Response wrapper for reservation lists
 * Note: The API returns a direct array, so this is used internally for consistency
 */
@Serializable
data class ReservationsResponse(
    @SerialName("reservations")
    val reservations: List<Reservation> = emptyList(),
    @SerialName("success")
    val success: Boolean = true,
    @SerialName("message")
    val message: String? = null
)

/**
 * Reservation data model matching the actual API response structure
 */
@Serializable
data class Reservation(
    @SerialName("id")
    val id: Int? = -1,
    @SerialName("tableName")
    val tableName: String? = null,
    @SerialName("reservationTime")
    val reservationTime: String? = null, // ISO format: "2025-07-18T11:30"
    @SerialName("numPeople")
    val numPeople: Int? = 0,
    @SerialName("customerName")
    val customerName: String? = null,
    @SerialName("customerPhone")
    val customerPhone: String? = null,
    @SerialName("reservationStatus")
    val reservationStatus: Int? = 0 // Default to Reserved if not provided by API
)

/**
 * Response model for create/update reservation API
 * Matches the response structure from the POST API
 */
@Serializable
data class CreateReservationResponse(
    @SerialName("id")
    val id: Int? = -1,
    @SerialName("storeId")
    val storeId: Int? = -1,
    @SerialName("tableId")
    val tableId: Int? = -1,
    @SerialName("customerId")
    val customerId: Int? = -1,
    @SerialName("reservationStatus")
    val reservationStatus: Int? = -1,
    @SerialName("guestName")
    val guestName: String? = null,
    @SerialName("guestPhone")
    val guestPhone: String? = null,
    @SerialName("reservationTime")
    val reservationTime: String? = null, // ISO format: "2025-07-22T23:30:00"
    @SerialName("numPeople")
    val numPeople: Int? = -1,
    @SerialName("createdAt")
    val createdAt: String? = null // ISO format: "2025-07-21T16:57:12.17"
)

/**
 * Area data model matching the areas API response structure
 * Endpoint: /api/reservations/areas?storeId=158
 */
@Serializable
data class Area(
    @SerialName("id")
    val id: Int,
    @SerialName("storeId")
    val storeId: Int,
    @SerialName("description")
    val description: String,
    @SerialName("areaDetailsJson")
    val areaDetailsJson: String? = null
)

/**
 * Table data model matching the tables API response structure
 * Endpoint: /api/reservations/tables?areaId=5
 */
@Serializable
@Parcelize
data class Table(
    @SerialName("id")
    val id: Int,
    @SerialName("storeId")
    val storeId: Int,
    @SerialName("areaId")
    val areaId: Int,
    @SerialName("tableName")
    val tableName: String,
    @SerialName("seatingCapacity")
    val seatingCapacity: Int,
    @SerialName("tableDetailsJson")
    val tableDetailsJson: String? = null,
    @SerialName("occupied")
    val occupied: Boolean = false,
    @SerialName("reserved")
    val reserved: Boolean = false,
    @SerialName("netPayable")
    val netPayable: Double? = null,
    @SerialName("areaName")
    val areaName: String? = null,
)  : Parcelable {
    /**
     * Parse the tableDetailsJson string into TableDetails object
     */
    fun getTableDetails(): TableDetails? {
        return tableDetailsJson?.let {
            try {
                val json = Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                }
                json.decodeFromString<TableDetails>(it)
            } catch (e: Exception) {
                null
            }
        }
    }
}

/**
 * Data class for parsing tableDetailsJson
 * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
 */
@Serializable
data class TableDetails(
    @SerialName("shape")
    val shape: String = "RECTANGLE", // RECTANGLE, SQUARE, CIRCLE, ROUND
    @SerialName("color")
    val color: String = "#2E7D32", // Default green color
    @SerialName("position")
    val position: TablePosition = TablePosition(),
    @SerialName("totalRows")
    val totalRows: Int = 8, // Default grid rows
    @SerialName("totalColumns")
    val totalColumns: Int = 8 // Default grid columns
)

/**
 * Data class for table position within the layout
 */
@Serializable
data class TablePosition(
    @SerialName("row")
    val row: Int = 1,
    @SerialName("col")
    val col: Int = 1
)
