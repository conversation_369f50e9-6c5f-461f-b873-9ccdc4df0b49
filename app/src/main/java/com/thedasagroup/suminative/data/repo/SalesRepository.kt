package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.api.BASE_URL
import com.thedasagroup.suminative.data.api.GET_SALES_REPORT
import com.thedasagroup.suminative.data.api.PRINT_SALES_REPORT
import com.thedasagroup.suminative.data.api.SALES
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.login.OrderRequest
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse
import com.thedasagroup.suminative.data.model.response.sales.SalesResponse
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_DATE_ONLY
import com.thedasagroup.suminative.ui.utils.formatDate
import io.ktor.client.call.body
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class SalesRepository(private val trueTimeImpl: TrueTimeImpl) : BaseRepository() {
    suspend fun  getTotalSales(request: SalesRequest): StateFlow<Async<SalesResponse>> {
        val flow = MutableStateFlow<Async<SalesResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val salesReponse = apiClient.post(urlString = SALES) {
                    contentType(ContentType.Application.Json)
                    parameter("startDate", request.startDate)
                    parameter("endDate", request.endDate)
                    parameter("storeId", request.storeId)
                }.body<SalesResponse>()
                return@safeApiCall Success(salesReponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getSalesReport(request: SalesRequest): StateFlow<Async<SalesReportResponse>> {
        val flow = MutableStateFlow<Async<SalesReportResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val salesReponse = apiClient.post(urlString = GET_SALES_REPORT) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<SalesReportResponse>()
                return@safeApiCall Success(salesReponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun printSalesReport(request: SalesRequest): StateFlow<Async<SalesReportResponse>> {
        val flow = MutableStateFlow<Async<SalesReportResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val salesReponse = apiClient.post(urlString = PRINT_SALES_REPORT) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<SalesReportResponse>()
                return@safeApiCall Success(salesReponse)
            }
            flow.value = response
        }
        return flow
    }
}