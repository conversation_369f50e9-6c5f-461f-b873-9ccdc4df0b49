package com.thedasagroup.suminative.data.api

import com.thedasagroup.suminative.data.model.response.reservations.Area
import com.thedasagroup.suminative.data.model.response.reservations.Reservation
import com.thedasagroup.suminative.data.model.response.reservations.Table
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * Retrofit service interface for Reservations API endpoints
 *
 * This service provides methods to interact with the reservations API using Retrofit.
 * The API returns direct arrays of reservations, not wrapped in response objects.
 */
interface ReservationsRetrofitService {

    /**
     * Get currently active reservations (next 45 minutes)
     *
     * @param storeId The store ID to filter reservations
     * @param currentTime Current time in ISO format (e.g., "2025-07-15T18:00:00")
     * @return Response containing a list of active reservations
     */
    @GET("BackendDASA-1.0.0/api/reservations/active")
    suspend fun getActiveReservations(
        @Query("storeId") storeId: Int,
        @Query("currentTime") currentTime: String,
        @Query("timezoneOffset") timezoneOffset: Int
    ): Response<List<Reservation>>

    /**
     * Get entire reservation history
     *
     * @param storeId The store ID to filter reservations
     * @param currentTime Current time in ISO format (e.g., "2025-07-15T18:00:00")
     * @return Response containing a list of all reservations
     */
    @GET("BackendDASA-1.0.0/api/reservations/all")
    suspend fun getAllReservations(
        @Query("storeId") storeId: Int,
        @Query("currentTime") currentTime: String
    ): Response<List<Reservation>>

    /**
     * Get reservation areas for a store
     *
     * @param storeId The store ID to filter areas
     * @return Response containing a list of areas
     */
    @GET("BackendDASA-1.0.0/api/reservations/areas")
    suspend fun getReservationAreas(
        @Query("storeId") storeId: Int
    ): Response<List<Area>>

    /**
     * Get tables for a specific area
     *
     * @param areaId The area ID to filter tables
     * @return Response containing a list of tables
     */
    @GET("BackendDASA-1.0.0/api/reservations/tables")
    suspend fun getReservationTables(
        @Query("areaId") areaId: Int
    ): Response<List<Table>>


    @GET("BackendDASA-1.0.0/api/tables/listTables")
    suspend fun getTables(
        @Query("storeId") storeId: Int,
        @Query("areaId") areaId: Int
    ): Response<List<Table>>

    /**
     * Get table by ID
     *
     * @param tableId The table ID to retrieve
     * @return Response containing the table details
     */
    @GET("BackendDASA-1.0.0/api/tables/getTableById")
    suspend fun getTableById(
        @Query("tableId") tableId: Int
    ): Response<Table>
}