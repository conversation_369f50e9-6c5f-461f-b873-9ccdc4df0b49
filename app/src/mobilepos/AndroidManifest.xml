<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.thedasagroup.suminative"
    >

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!--    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <queries>
        <package android:name="com.sunmi.scanner" />
        <package android:name="com.sunmi.sunmiqrcodescanner" />
    </queries>

    <application
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.SumiNative"
        tools:targetApi="31"
        android:name=".App"
        android:usesCleartextTraffic="true"
        android:largeHeap="true"
        >

        <receiver android:enabled="true" android:name=".ui.service.StartReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

        <service
            android:name=".ui.service.EndlessSocketService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            >
        </service>

        <activity
            android:name=".ui.stores.SelectStoreActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.SumiNative"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".ui.login.LoginActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />
        <activity android:name=".ui.user_profile.SelectUserProfileActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />
        <activity android:name=".ui.MainActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />
        <activity android:name=".ui.tracking.TrackingActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />
        <activity android:name=".ui.stores.ClosedStoreActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />
        <activity android:name=".ui.stock.StockActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity android:name=".ui.sales.SalesActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity android:name=".ui.payment.PaymentActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity android:name=".ui.payment.CashPaymentActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity android:name=".ui.guava_orders.GuavaOrdersActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity android:name=".ui.pos_orders.POSOrdersActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity android:name=".ui.refund.RefundSumUpActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity android:name=".ui.categories.CategoriesActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity android:name=".ui.splitbill.SplitBillActivity"
            android:screenOrientation="portrait"
            />

        <activity android:name=".ui.reservations.ReservationsActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi" />

        <activity android:name=".ui.rewards.RewardsActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />

        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:label="Settings"
            android:screenOrientation="portrait"
            android:parentActivityName=".ui.MainActivity">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".ui.MainActivity" />
        </activity>

        <activity android:name=".ui.stores.DownloadProductsActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi" />

        <activity android:name=".ui.local_orders.LocalOrdersActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi" />

        <activity android:name=".ui.reservations.AreaTableSelectionActivity"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi"
            />


        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            tools:node="remove" />

    </application>

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="https" />
        </intent>
    </queries>


    <!--<uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />-->

</manifest>