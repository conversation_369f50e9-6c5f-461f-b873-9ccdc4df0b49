# Void Notification Usage Examples

This document provides practical examples of how to use the void notification feature in different scenarios.

## Example 1: Void Items from a Table Order

```kotlin
// In your Composable or Fragment
fun voidItemsFromTable(
    viewModel: ProductsScreenViewModel,
    courseName: String,
    tableName: String,
    itemsToVoid: List<Cart>
) {
    // Simply call the ViewModel function
    viewModel.sendVoidNotification(
        courseName = courseName,
        tableName = tableName,
        carts = itemsToVoid
    )
    
    // The ViewModel handles the response automatically
    // Check logs for success/failure messages
}
```

## Example 2: Void Takeaway Order Items

```kotlin
// When voiding items from a takeaway order
fun voidTakeawayItems(
    viewModel: ProductsScreenViewModel,
    itemsToVoid: List<Cart>
) {
    viewModel.sendVoidNotification(
        courseName = "Starters", // or the actual course name
        tableName = "Takeaway",
        carts = itemsToVoid
    )
}
```

## Example 3: Void with Custom Response Handling

If you need custom response handling, you can call the use case directly:

```kotlin
// In your ViewModel or custom function
fun voidItemsWithCustomHandling(
    storeId: Int,
    courseName: String,
    tableName: String,
    carts: List<Cart>,
    onSuccess: () -> Unit,
    onError: (String) -> Unit
) {
    viewModelScope.launch {
        sendVoidNotificationUseCase(
            storeId = storeId,
            courseName = courseName,
            tableName = tableName,
            carts = carts
        ).collect { response ->
            when (response) {
                is Success -> {
                    val result = response.invoke()
                    if (result.success) {
                        onSuccess()
                    } else {
                        onError(result.message ?: "Unknown error")
                    }
                }
                is Fail -> {
                    onError(response.error.message ?: "Network error")
                }
                is Loading -> {
                    // Show loading indicator
                }
                else -> {
                    // Handle uninitialized state
                }
            }
        }
    }
}
```

## Example 4: Void with JSON String

If you already have the cart data as a JSON string:

```kotlin
fun voidItemsWithJson(
    viewModel: ProductsScreenViewModel,
    courseName: String,
    tableName: String,
    cartJson: String
) {
    viewModel.sendVoidNotification(
        courseName = courseName,
        tableName = tableName,
        cartJson = cartJson
    )
}
```

## Example 5: Void Multiple Courses

```kotlin
fun voidMultipleCourses(
    viewModel: ProductsScreenViewModel,
    tableName: String,
    courseItems: Map<String, List<Cart>> // courseName -> items
) {
    courseItems.forEach { (courseName, items) ->
        viewModel.sendVoidNotification(
            courseName = courseName,
            tableName = tableName,
            carts = items
        )
    }
}
```

## Example 6: Void with State Management

Using MvRx state for UI updates:

```kotlin
// In your ViewModel
data class VoidState(
    val isVoiding: Boolean = false,
    val voidSuccess: Boolean = false,
    val voidError: String? = null
) : MavericksState

fun voidItemsWithState(
    courseName: String,
    tableName: String,
    carts: List<Cart>
) {
    setState { copy(isVoiding = true, voidError = null) }
    
    viewModelScope.launch {
        val storeId = prefs.store?.id ?: run {
            setState { copy(isVoiding = false, voidError = "Store not found") }
            return@launch
        }
        
        sendVoidNotificationUseCase(
            storeId = storeId,
            courseName = courseName,
            tableName = tableName,
            carts = carts
        ).collect { response ->
            when (response) {
                is Success -> {
                    val result = response.invoke()
                    setState {
                        copy(
                            isVoiding = false,
                            voidSuccess = result.success,
                            voidError = if (!result.success) result.message else null
                        )
                    }
                }
                is Fail -> {
                    setState {
                        copy(
                            isVoiding = false,
                            voidSuccess = false,
                            voidError = response.error.message
                        )
                    }
                }
                else -> {
                    // Keep loading state
                }
            }
        }
    }
}
```

## Example 7: Void with Confirmation Dialog

```kotlin
@Composable
fun VoidItemsButton(
    viewModel: ProductsScreenViewModel,
    courseName: String,
    tableName: String,
    items: List<Cart>
) {
    var showDialog by remember { mutableStateOf(false) }
    
    Button(onClick = { showDialog = true }) {
        Text("Void Items")
    }
    
    if (showDialog) {
        AlertDialog(
            onDismissRequest = { showDialog = false },
            title = { Text("Confirm Void") },
            text = { Text("Are you sure you want to void ${items.size} item(s)?") },
            confirmButton = {
                Button(
                    onClick = {
                        viewModel.sendVoidNotification(
                            courseName = courseName,
                            tableName = tableName,
                            carts = items
                        )
                        showDialog = false
                    }
                ) {
                    Text("Confirm")
                }
            },
            dismissButton = {
                Button(onClick = { showDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}
```

## Example 8: Batch Void with Progress

```kotlin
fun voidItemsInBatches(
    viewModel: ProductsScreenViewModel,
    tableName: String,
    courseItems: Map<String, List<Cart>>,
    onProgress: (Int, Int) -> Unit, // current, total
    onComplete: () -> Unit
) {
    viewModelScope.launch {
        val total = courseItems.size
        var current = 0
        
        courseItems.forEach { (courseName, items) ->
            viewModel.sendVoidNotification(
                courseName = courseName,
                tableName = tableName,
                carts = items
            )
            
            current++
            onProgress(current, total)
            
            // Optional: Add delay between batches
            delay(500)
        }
        
        onComplete()
    }
}
```

## Example 9: Void with Retry Logic

```kotlin
suspend fun voidItemsWithRetry(
    useCase: SendVoidNotificationUseCase,
    storeId: Int,
    courseName: String,
    tableName: String,
    carts: List<Cart>,
    maxRetries: Int = 3
): Boolean {
    repeat(maxRetries) { attempt ->
        val response = useCase(storeId, courseName, tableName, carts)
        
        when (val result = response.value) {
            is Success -> {
                val voidResponse = result.invoke()
                if (voidResponse.success) {
                    return true
                }
            }
            is Fail -> {
                if (attempt < maxRetries - 1) {
                    delay(1000 * (attempt + 1)) // Exponential backoff
                }
            }
            else -> {
                // Wait for response
                delay(2000)
            }
        }
    }
    return false
}
```

## Example 10: Integration with Existing Order Flow

```kotlin
// In ProductsScreenViewModel
fun voidOrderItems(
    orderId: Int,
    courseName: String,
    tableName: String
) {
    viewModelScope.launch {
        // Get the order
        val order = state.tableOrders[orderId] ?: return@launch
        
        // Filter items to void (e.g., items with a specific status)
        val itemsToVoid = order.carts?.filter { cart ->
            // Your void criteria here
            cart.storeItem?.id != null
        } ?: emptyList()
        
        if (itemsToVoid.isEmpty()) {
            Timber.w("No items to void")
            return@launch
        }
        
        // Send void notification
        sendVoidNotification(
            courseName = courseName,
            tableName = tableName,
            carts = itemsToVoid
        )
        
        // Update local state after successful void
        // (You might want to wait for the response before updating)
        setState {
            val updatedOrder = order.copy(
                carts = order.carts?.filterNot { it in itemsToVoid }
            )
            copy(
                tableOrders = tableOrders.toMutableMap().apply {
                    put(orderId, updatedOrder)
                }
            )
        }
    }
}
```

## Testing Examples

### Unit Test Example
```kotlin
@Test
fun `test void notification success`() = runBlocking {
    // Arrange
    val carts = listOf(
        Cart(
            storeItem = StoreItem(id = 1, name = "Item 1"),
            quantity = 1,
            netPayable = 10.0
        )
    )
    
    val expectedResponse = VoidNotificationResponse(
        success = true,
        statusCode = 200
    )
    
    coEvery { 
        reservationsRepository.sendVoidNotification(any(), any(), any(), any()) 
    } returns MutableStateFlow(Success(expectedResponse))
    
    // Act
    val result = useCase(158, "Starters", "T-12", carts)
    
    // Assert
    assert(result.value is Success)
    assert((result.value as Success).invoke().success)
}
```

## Common Patterns

### Pattern 1: Void and Refresh
```kotlin
fun voidAndRefresh(items: List<Cart>) {
    viewModel.sendVoidNotification(
        courseName = "Starters",
        tableName = "T-12",
        carts = items
    )
    
    // Refresh the order list
    viewModel.refreshOrders()
}
```

### Pattern 2: Void with Undo
```kotlin
var lastVoidedItems: List<Cart>? = null

fun voidWithUndo(items: List<Cart>) {
    lastVoidedItems = items
    viewModel.sendVoidNotification(
        courseName = "Starters",
        tableName = "T-12",
        carts = items
    )
}

fun undoVoid() {
    lastVoidedItems?.let { items ->
        // Re-add items to order
        // (Implementation depends on your order management logic)
    }
}
```

## Best Practices

1. **Always validate input** before calling void notification
2. **Handle errors gracefully** with user-friendly messages
3. **Log important events** for debugging
4. **Consider confirmation dialogs** for destructive actions
5. **Update local state** after successful void
6. **Use retry logic** for network failures
7. **Batch operations** when voiding multiple courses
8. **Test thoroughly** with different scenarios

## Notes

- The `storeId` is automatically retrieved from preferences in ViewModel methods
- All operations are asynchronous and use coroutines
- Responses are wrapped in `Async<T>` types for reactive updates
- The cart JSON is automatically generated when using `List<Cart>`

