# Void Notification Implementation

## Overview
This document describes the implementation of the void notification API endpoint in the DasaPOS application. The void notification feature allows the system to send notifications to printers when items are voided from orders.

## API Endpoint
```
POST https://dasasplace.com/BackendDASA-1.0.0/api/printer/voidNotification
```

### Request Format
```json
{
    "storeId": 158,
    "courseName": "Starters",
    "tableName": "Takeaway",
    "cartJson": "[{...cart items...}]"
}
```

### Response Format
```json
{
    "success": true,
    "message": null,
    "data": null,
    "statusCode": 200
}
```

## Implementation Details

### 1. Request Model
**File:** `app/src/main/java/com/thedasagroup/suminative/data/model/request/print/VoidNotificationRequest.kt`

```kotlin
@Serializable
data class VoidNotificationRequest(
    @SerialName("storeId")
    val storeId: Int,
    @SerialName("courseName")
    val courseName: String,
    @SerialName("tableName")
    val tableName: String,
    @SerialName("cartJson")
    val cartJson: String
)
```

### 2. Response Model
**File:** `app/src/main/java/com/thedasagroup/suminative/data/model/response/void_notification/VoidNotificationResponse.kt`

```kotlin
@Serializable
data class VoidNotificationResponse(
    @SerialName("success")
    val success: Boolean = false,
    @SerialName("message")
    val message: String? = null,
    @SerialName("data")
    val data: String? = null,
    @SerialName("statusCode")
    val statusCode: Int? = null
)
```

### 3. API Endpoint Constant
**File:** `app/src/main/java/com/thedasagroup/suminative/data/api/ApiClient.kt`

Added:
```kotlin
val VOID_NOTIFICATION = "${BASE_DOMAIN}/BackendDASA-1.0.0/api/printer/voidNotification"
```

### 4. Repository Functions
**File:** `app/src/main/java/com/thedasagroup/suminative/data/repo/ReservationsRepository.kt`

Two overloaded functions were added:

#### Function 1: Using Request Object
```kotlin
suspend fun sendVoidNotification(
    request: VoidNotificationRequest
): StateFlow<Async<VoidNotificationResponse>>
```

#### Function 2: Using Individual Parameters (Convenience Method)
```kotlin
suspend fun sendVoidNotification(
    storeId: Int,
    courseName: String,
    tableName: String,
    cartJson: String
): StateFlow<Async<VoidNotificationResponse>>
```

### 5. Use Case
**File:** `app/src/main/java/com/thedasagroup/suminative/domain/void_notification/SendVoidNotificationUseCase.kt`

The use case provides three overloaded invoke methods:

#### Method 1: Using Request Object
```kotlin
suspend operator fun invoke(
    request: VoidNotificationRequest
): StateFlow<Async<VoidNotificationResponse>>
```

#### Method 2: Using Individual Parameters
```kotlin
suspend operator fun invoke(
    storeId: Int,
    courseName: String,
    tableName: String,
    cartJson: String
): StateFlow<Async<VoidNotificationResponse>>
```

#### Method 3: Using Cart List
```kotlin
suspend operator fun invoke(
    storeId: Int,
    courseName: String,
    tableName: String,
    carts: List<Cart>
): StateFlow<Async<VoidNotificationResponse>>
```

### 6. Dependency Injection
**File:** `app/src/main/java/com/thedasagroup/suminative/di/AppUseCaseModule.kt`

Added provider:
```kotlin
@Provides
@Singleton
fun providesSendVoidNotificationUseCase(
    reservationsRepository: ReservationsRepository
): SendVoidNotificationUseCase {
    return SendVoidNotificationUseCase(reservationsRepository = reservationsRepository)
}
```

### 7. ViewModel Integration
**File:** `app/src/main/java/com/thedasagroup/suminative/ui/products/ProductsScreenViewModel.kt`

#### Constructor Parameter Added
```kotlin
val sendVoidNotificationUseCase: SendVoidNotificationUseCase
```

#### Two Public Functions Added

##### Function 1: Send Void Notification with Cart List
```kotlin
fun sendVoidNotification(
    courseName: String,
    tableName: String,
    carts: List<Cart>
)
```

##### Function 2: Send Void Notification with JSON String
```kotlin
fun sendVoidNotification(
    courseName: String,
    tableName: String,
    cartJson: String
)
```

## Usage Examples

### Example 1: Using Cart List
```kotlin
viewModel.sendVoidNotification(
    courseName = "Starters",
    tableName = "Takeaway",
    carts = listOf(
        Cart(
            storeItem = storeItem,
            quantity = 1,
            price = 11.7,
            // ... other cart properties
        )
    )
)
```

### Example 2: Using JSON String
```kotlin
val cartJson = """[{"storeItem":{...},"quantity":1,...}]"""
viewModel.sendVoidNotification(
    courseName = "Starters",
    tableName = "Takeaway",
    cartJson = cartJson
)
```

### Example 3: Direct Use Case Call
```kotlin
viewModelScope.launch {
    val storeId = prefs.store?.id ?: return@launch
    
    sendVoidNotificationUseCase(
        storeId = storeId,
        courseName = "Starters",
        tableName = "Takeaway",
        carts = cartList
    ).collect { response ->
        when (response) {
            is Success -> {
                val result = response.invoke()
                if (result.success) {
                    // Handle success
                }
            }
            is Fail -> {
                // Handle error
            }
            else -> {
                // Handle loading state
            }
        }
    }
}
```

## Architecture Pattern

The implementation follows the clean architecture pattern used throughout the application:

1. **Data Layer**
   - Request/Response models with Kotlinx Serialization
   - Repository with Ktor HTTP client for API calls
   - API endpoint constants

2. **Domain Layer**
   - Use case encapsulating business logic
   - Multiple invoke methods for flexibility

3. **Presentation Layer**
   - ViewModel integration with MvRx
   - Convenient methods for UI to call

## Error Handling

The implementation uses the existing `safeApiCall` wrapper from `BaseRepository` which handles:
- Network errors
- Serialization errors
- HTTP errors
- Timeout errors

Responses are wrapped in `Async<T>` types:
- `Loading` - Request in progress
- `Success<T>` - Successful response
- `Fail` - Error occurred
- `Uninitialized` - Not yet called

## Testing Recommendations

1. **Unit Tests**
   - Test use case with mocked repository
   - Test repository with mocked HTTP client
   - Test request/response serialization

2. **Integration Tests**
   - Test end-to-end API call
   - Test error scenarios (network failure, 500 errors, etc.)
   - Test with actual cart JSON data

3. **UI Tests**
   - Test ViewModel functions
   - Test state updates on success/failure
   - Test with different cart configurations

## Notes

- The implementation uses Ktor HTTP client (not Retrofit) for consistency with other printer endpoints
- The repository is placed in `ReservationsRepository` as requested
- The use case follows the same pattern as `SendCoursesNotificationUseCase`
- The ViewModel functions automatically get the storeId from preferences
- All functions use coroutines and StateFlow for reactive updates

