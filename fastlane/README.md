fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android test

```sh
[bundle exec] fastlane android test
```

Runs all the tests

### android prodGeneral

```sh
[bundle exec] fastlane android prodGeneral
```

Upload Prod General to Google Drive

### android stagingGeneral

```sh
[bundle exec] fastlane android stagingGeneral
```

Upload Staging General to Google Drive

### android prodPos2

```sh
[bundle exec] fastlane android prodPos2
```

Upload Prod Sumni D3 Mini Apk to Google Drive

### android prodPos3

```sh
[bundle exec] fastlane android prodPos3
```

Upload Prod Sumni TS2 Apk to Google Drive

### android uploadGoogleDriveAll

```sh
[bundle exec] fastlane android uploadGoogleDriveAll
```



### android uploadDPOSStaging

```sh
[bundle exec] fastlane android uploadDPOSStaging
```

Upload all staging APKs to Google Drive

### android uploadDPOSProd

```sh
[bundle exec] fastlane android uploadDPOSProd
```

Upload all prod APKs to Google Drive

### android uploadDPOSOnlyProdRelease

```sh
[bundle exec] fastlane android uploadDPOSOnlyProdRelease
```

Upload all prod APKs to Google Drive

### android uploadD3ProOnlyProdRelease

```sh
[bundle exec] fastlane android uploadD3ProOnlyProdRelease
```

Upload all D3 Pro APKs to Google Drive

### android uploadDPOSStagingOnly

```sh
[bundle exec] fastlane android uploadDPOSStagingOnly
```

Upload all staging APKs to Google Drive

### android uploadDPOSStagingProdRelease

```sh
[bundle exec] fastlane android uploadDPOSStagingProdRelease
```

Upload all staging and prod releaseAPKs to Google Drive

### android uploadMPOSOnlyProdRelease

```sh
[bundle exec] fastlane android uploadMPOSOnlyProdRelease
```

Upload all prod APKs to Google Drive

### android prod

```sh
[bundle exec] fastlane android prod
```

Submit a new Beta Build to Crashlytics Beta

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
